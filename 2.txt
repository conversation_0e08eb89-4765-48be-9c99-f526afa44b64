mysql> USE tos_lobby;
Database changed

mysql> SHOW CREATE PROCEDURE sp_service_list;
+-----------------+-------------------------------------------------------------------------------------------+---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------+----------------------+----------------------+--------------------+
| Procedure       | sql_mode                                                                                  | Create Procedure                                                                                                                                                                                                                                                                                                                                                                                                                                                                                  | character_set_client | collation_connection | Database Collation |
+-----------------+-------------------------------------------------------------------------------------------+---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------+----------------------+----------------------+--------------------+
| sp_service_list | STRICT_TRANS_TABLES,ERROR_FOR_DIVISION_BY_ZERO,NO_AUTO_CREATE_USER,NO_ENGINE_SUBSTITUTION | CREATE DEFINER=`root`@`localhost` PROCEDURE `sp_service_list`()
sp_label: BEGIN






	SELECT 	`class_id`,
			`name`,
			`max_user`,
			`session_sn`,
			`age_limit`,
			`traffic_url`,
			`global_max_user`,
            `auto_channel_user`
	FROM 	`service_group`;

	SELECT 	`class_id`,
			`group_id`,
			`ip`,
			`name`,
			`external_ip`,
			`disable`
	FROM 	`service_machine`;

	SELECT 	`class_id`,
			`machine_id`,
			`port`,
			`kind`,
			`name`,
			`param`
	FROM 	`service_daemon`;

	SELECT 	`class_id`,
			`map_name`,
			`channel_id`,
			`daemon_id`,
			`desc`
	FROM 	`service_zone`;


END | utf8mb4              | utf8mb4_general_ci   | utf8mb4_general_ci |
+-----------------+-------------------------------------------------------------------------------------------+---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------+----------------------+----------------------+--------------------+
1 <USER> <GROUP> set (0.04 sec)
