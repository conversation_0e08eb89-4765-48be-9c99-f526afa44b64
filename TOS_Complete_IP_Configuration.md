# TOS 私服完整IP配置修改清单

## 🎯 完整的IP修改清单（按重要性排序）

### 🔴 第一优先级：客户端连接配置（必须改外网IP）

#### 1. 客户端主配置文件
**文件：** `r1/data/xml_service/serverconfig.xml`
```xml
<!-- 127.0.0.1 → ************** -->
<GameOption ServerListURL="http://**************/serverlist.xml"
           NewAccountURL="http://**************:8011"
           PaymentURL="http://**************:8011"
           LoadingImgURL="http://**************/loadingimg/"
           LoadingImgCount="10"/>
```

#### 2. 全局客户端配置
**文件：** `r1/data/global/xml_service/serverconfig.xml`
```xml
<!-- 127.0.0.1 → ************** -->
<GameOption ServerListURL="http://**************/tos/serverlist.xml"
           NewAccountURL="http://**************:8011"
           PaymentURL="http://**************:8011"
           LoadingImgURL="http://**************/tos/loadingimg/"
           LoadingImgCount="10"/>
```

#### 3. 服务器列表配置
**文件：** `r1/server_release/serverlist.xml`
```xml
<!-- 127.0.0.1 → ************** -->
<serverlist>
    <statistics Ip="**************" Port="10001"/>
    <server GROUP_ID="1001" Server0_IP="**************" Server0_Port="7001" Server1_IP="**************" Server1_Port="7001"/>
    <server GROUP_ID="101" Server0_IP="**************" Server0_Port="2000"/>
    <server GROUP_ID="2001" Server0_IP="**************" Server0_Port="2000"/>
</serverlist>
```

#### 4. PHPStudy客户端更新配置
**文件：** `phpstudy_pro/WWW/toslive/update/release/client.xml`
```xml
<!-- *************** → ************** -->
<GameOption ServerListURL="http://**************/toslive/update/serverlist.xml"
           StaticConfigURL="http://**************/toslive/update/"
           NewAccountURL="http://**************/"
           PaymentURL="http://**************/"
           LoadingImgURL="http://**************/toslive/update/loadingimg/"/>
```

#### 5. PHPStudy服务器列表
**文件：** `phpstudy_pro/WWW/toslive/update/serverlist.xml`
**文件：** `phpstudy_pro/WWW/toslive/launcher/serverlist.xml`
```xml
<!-- *************** → ************** -->
<statistics Ip="**************" Port="10001"/>
<server GROUP_ID="9001" Server0_IP="**************" Server0_Port="2005" Server1_IP="**************" Server1_Port="2005"/>
```

### 🟡 第二优先级：服务器管理和集群通信

#### 6. Hub管理器启动脚本
**文件：** `r1/_1_HUB MID 1.bat`
```batch
REM *************** → **************（如果需要外部管理）
start ldManagerHub.exe --mode=dev -PORT 9676 -IP ************** -SGID 9001 -MID 1 -ROOT C:\r1
```

#### 7. Node管理器启动脚本
**文件：** `r1/_2_NODE MID 1.bat`
```batch
REM *************** → **************（如果需要外部管理）
start ldManagerNode.exe -IP ************** -PORT 9676 -SGID 9001 -OPEN_PORT 8102 -MID 1 -ROOT C:\r1
```

#### 8. Web服务配置（公会系统）
**文件：** `r1/server_release/WebService/guild/exe/WebServices.exe.config`
```xml
<!-- 数据库保持127.0.0.1，管理IP改************** -->
<add name="Mariadb" connectionString="host=127.0.0.1;port=3306;database=tos_world1;" />
<add name="Mariadb_lobby" connectionString="host=127.0.0.1;port=3306;database=tos_lobby;" />
<add name="serverManagerIP" connectionString="**************" />
<add name="validIPList" connectionString="**************;127.0.0.1;::1;" />
```

#### 9. Web服务配置（市场系统）
**文件：** `r1/server_release/WebService/market/exe/WebServices.exe.config`
```xml
<!-- 同样的修改 -->
<add name="Mariadb" connectionString="host=127.0.0.1;port=3306;database=tos_world1;" />
<add name="Mariadb_lobby" connectionString="host=127.0.0.1;port=3306;database=tos_lobby;" />
<add name="serverManagerIP" connectionString="**************" />
<add name="validIPList" connectionString="**************;127.0.0.1;::1;" />
```

### 🟢 第三优先级：保持不变的配置（127.0.0.1）

#### 10. 服务器内部通信（不要修改）
**文件：** `r1/data/xml_service/machine.xml`
```xml
<!-- 保持127.0.0.1，这是内部通信 -->
<Class ClassID="1" GroupID="1001" IP="L127.0.0.1" Name="1ststory"/>
<Class ClassID="2" GroupID="1002" IP="L127.0.0.1" Name="R1_Main"/>
```

#### 11. 守护进程配置（不要修改）
**文件：** `r1/data/xml_service/daemon.xml`
```xml
<!-- 保持127.0.0.1，这是本机进程绑定 -->
<Class ClassID="1001" MachineID="1" Port="8000" IP="127.0.0.1"/>
<Class ClassID="1002" MachineID="1" Port="2000" IP="127.0.0.1"/>
```

## 🛠️ 一键完整修改脚本

### Windows批处理脚本
```batch
@echo off
set SERVER_IP=**************

echo ================================
echo TOS 私服完整IP配置修改工具
echo 外网IP: %SERVER_IP%
echo ================================

echo.
echo [1/9] 修改客户端主配置...
powershell -Command "(gc 'r1\data\xml_service\serverconfig.xml') -replace '127\.0\.0\.1', '%SERVER_IP%' | Out-File -encoding UTF8 'r1\data\xml_service\serverconfig.xml'"

echo [2/9] 修改全局客户端配置...
powershell -Command "(gc 'r1\data\global\xml_service\serverconfig.xml') -replace '127\.0\.0\.1', '%SERVER_IP%' | Out-File -encoding UTF8 'r1\data\global\xml_service\serverconfig.xml'"

echo [3/9] 修改服务器列表...
powershell -Command "(gc 'r1\server_release\serverlist.xml') -replace '127\.0\.0\.1', '%SERVER_IP%' | Out-File -encoding UTF8 'r1\server_release\serverlist.xml'"

echo [4/9] 修改PHPStudy客户端配置...
powershell -Command "(gc 'phpstudy_pro\WWW\toslive\update\release\client.xml') -replace '192\.168\.200\.188', '%SERVER_IP%' | Out-File -encoding UTF8 'phpstudy_pro\WWW\toslive\update\release\client.xml'"

echo [5/9] 修改PHPStudy服务器列表...
powershell -Command "(gc 'phpstudy_pro\WWW\toslive\update\serverlist.xml') -replace '192\.168\.200\.188', '%SERVER_IP%' | Out-File -encoding UTF8 'phpstudy_pro\WWW\toslive\update\serverlist.xml'"
powershell -Command "(gc 'phpstudy_pro\WWW\toslive\launcher\serverlist.xml') -replace '192\.168\.200\.188', '%SERVER_IP%' | Out-File -encoding UTF8 'phpstudy_pro\WWW\toslive\launcher\serverlist.xml'"

echo [6/9] 修改Hub管理器启动脚本...
powershell -Command "(gc 'r1\_1_HUB MID 1.bat') -replace '192\.168\.200\.133', '%SERVER_IP%' | Out-File -encoding UTF8 'r1\_1_HUB MID 1.bat'"

echo [7/9] 修改Node管理器启动脚本...
powershell -Command "(gc 'r1\_2_NODE MID 1.bat') -replace '192\.168\.200\.133', '%SERVER_IP%' | Out-File -encoding UTF8 'r1\_2_NODE MID 1.bat'"

echo [8/9] 修改公会Web服务配置...
powershell -Command "(gc 'r1\server_release\WebService\guild\exe\WebServices.exe.config') -replace 'serverManagerIP.*connectionString=\"[^\"]*\"', 'serverManagerIP\" connectionString=\"%SERVER_IP%\"' | Out-File -encoding UTF8 'r1\server_release\WebService\guild\exe\WebServices.exe.config'"
powershell -Command "(gc 'r1\server_release\WebService\guild\exe\WebServices.exe.config') -replace 'validIPList.*connectionString=\"[^\"]*\"', 'validIPList\" connectionString=\"%SERVER_IP%;127.0.0.1;::1;\"' | Out-File -encoding UTF8 'r1\server_release\WebService\guild\exe\WebServices.exe.config'"

echo [9/9] 修改市场Web服务配置...
powershell -Command "(gc 'r1\server_release\WebService\market\exe\WebServices.exe.config') -replace 'serverManagerIP.*connectionString=\"[^\"]*\"', 'serverManagerIP\" connectionString=\"%SERVER_IP%\"' | Out-File -encoding UTF8 'r1\server_release\WebService\market\exe\WebServices.exe.config'"
powershell -Command "(gc 'r1\server_release\WebService\market\exe\WebServices.exe.config') -replace 'validIPList.*connectionString=\"[^\"]*\"', 'validIPList\" connectionString=\"%SERVER_IP%;127.0.0.1;::1;\"' | Out-File -encoding UTF8 'r1\server_release\WebService\market\exe\WebServices.exe.config'"

echo.
echo ================================
echo ✅ 完整IP配置修改完成！
echo ================================
echo.
echo 已修改的配置：
echo ✓ 客户端连接配置 (serverconfig.xml)
echo ✓ 服务器列表配置 (serverlist.xml)
echo ✓ PHPStudy Web配置 (client.xml)
echo ✓ 管理器启动脚本 (Hub/Node)
echo ✓ Web服务配置 (guild/market)
echo.
echo 保持不变的配置：
echo ✓ 服务器内部通信 (machine.xml)
echo ✓ 守护进程绑定 (daemon.xml)
echo ✓ 数据库连接 (127.0.0.1)
echo.
echo 外网IP: **************
echo.
echo 现在可以启动服务器了！
pause
```

## 🚀 启动顺序建议

### 1. 启动基础服务
```batch
# 启动数据库
启动 MySQL/MariaDB

# 启动Web服务器
启动 PHPStudy Pro
```

### 2. 启动游戏服务器
```batch
# 按顺序启动
1. r1\_1_HUB MID 1.bat
2. r1\_2_NODE MID 1.bat  
3. r1\_3_GLOBAL.bat
4. r1\_4_BARRACK.bat
5. r1\_5_ZONE.bat
```

### 3. 验证服务状态
```batch
# 检查端口监听
netstat -an | findstr "80 2000 7001 8000 8011 8101 8102 9676 10001"

# 测试Web访问
curl http://**************/toslive/update/serverlist.xml
```

## 📋 配置文件功能对照表

| 配置文件 | 修改内容 | 对应功能 | 客户端影响 |
|----------|----------|----------|------------|
| `serverconfig.xml` | 127.0.0.1→************** | 客户端连接配置 | 直接影响客户端启动 |
| `serverlist.xml` | 127.0.0.1→************** | 游戏服务器地址 | 直接影响游戏连接 |
| `client.xml` | 192.168→************** | 更新服务地址 | 影响客户端更新 |
| `WebServices.exe.config` | 管理IP→************** | Web API访问 | 影响网页功能 |
| `_1_HUB MID 1.bat` | 192.168→************** | 集群管理 | 影响服务器管理 |
| `_2_NODE MID 1.bat` | 192.168→************** | 节点管理 | 影响服务器管理 |

## ⚠️ 重要提醒

1. **备份原始文件**：修改前务必备份所有配置文件
2. **防火墙设置**：确保外网IP的相关端口已开放
3. **DNS解析**：如果使用域名，确保DNS正确解析到外网IP
4. **安全考虑**：不要暴露不必要的内部服务端口
5. **测试验证**：修改后逐步测试各项功能是否正常

完成这些修改后，你的TOS私服就能完整地对外提供服务了！