# TOS 私服 IP 地址配置和防火墙端口开放指南

## 📋 需要修改的IP地址配置文件

### 1. 客户端配置文件
**文件路径：** `r1/data/xml_service/serverconfig.xml`
```xml
<!-- 修改前 -->
<GameOption ServerListURL="http://127.0.0.1/serverlist.xml" NewAccountURL="http://127.0.0.1:8011" PaymentURL="http://127.0.0.1:8011" LoadingImgURL="http://127.0.0.1/loadingimg/" LoadingImgCount="10"/>

<!-- 修改后 -->
<GameOption ServerListURL="http://你的服务器IP/serverlist.xml" NewAccountURL="http://你的服务器IP:8011" PaymentURL="http://你的服务器IP:8011" LoadingImgURL="http://你的服务器IP/loadingimg/" LoadingImgCount="10"/>
```

### 2. 服务器列表配置
**文件路径：** `r1/server_release/serverlist.xml`
```xml
<!-- 修改前 -->
<statistics Ip="127.0.0.1" Port="10001"/>
<server GROUP_ID="1001" TRAFFIC="0" ENTER_LIMIT="100" NAME="메인서버" Server0_IP="127.0.0.1" Server0_Port="7001" Server1_IP="127.0.0.1" Server1_Port="7001"/>
<server GROUP_ID="101" TRAFFIC="0" ENTER_LIMIT="100" NAME="KOR서버" Server0_IP="127.0.0.1" Server0_Port="2000" Server1_IP="None" Server1_Port="None"/>

<!-- 修改后 -->
<statistics Ip="你的服务器IP" Port="10001"/>
<server GROUP_ID="1001" TRAFFIC="0" ENTER_LIMIT="100" NAME="메인서버" Server0_IP="你的服务器IP" Server0_Port="7001" Server1_IP="你的服务器IP" Server1_Port="7001"/>
<server GROUP_ID="101" TRAFFIC="0" ENTER_LIMIT="100" NAME="KOR서버" Server0_IP="你的服务器IP" Server0_Port="2000" Server1_IP="None" Server1_Port="None"/>
```

### 3. Web服务配置
**文件路径：** `r1/server_release/WebService/guild/exe/WebServices.exe.config`
```xml
<!-- 修改前 -->
<add name="Mariadb" connectionString="host=127.0.0.1;port=3306;database=tos_world1;" />
<add name="Mariadb_lobby" connectionString="host=127.0.0.1;port=3306;database=tos_lobby;" />
<add name="serverManagerIP" connectionString="************" />
<add name="validIPList" connectionString="::1;***********;************;************;************;" />

<!-- 修改后 -->
<add name="Mariadb" connectionString="host=你的服务器IP;port=3306;database=tos_world1;" />
<add name="Mariadb_lobby" connectionString="host=你的服务器IP;port=3306;database=tos_lobby;" />
<add name="serverManagerIP" connectionString="你的服务器IP" />
<add name="validIPList" connectionString="你的服务器IP;::1;" />
```

### 4. 机器配置文件
**文件路径：** `r1/data/xml_service/machine.xml`
```xml
<!-- 修改前 -->
<Class ClassID="1" GroupID="1001" IP="L127.0.0.1" Name="1ststory"/>
<Class ClassID="2" GroupID="1002" IP="L127.0.0.1" Name="R1_Main"/>

<!-- 修改后 -->
<Class ClassID="1" GroupID="1001" IP="L你的服务器IP" Name="1ststory"/>
<Class ClassID="2" GroupID="1002" IP="L你的服务器IP" Name="R1_Main"/>
```

### 5. 全局服务器配置
**文件路径：** `r1/data/global/xml_service/serverconfig.xml`
```xml
<!-- 修改前 -->
<GameOption ServerListURL="http://127.0.0.1/tos/serverlist.xml" NewAccountURL="http://127.0.0.1:8011" PaymentURL="http://127.0.0.1:8011" LoadingImgURL="http://127.0.0.1/tos/loadingimg/" LoadingImgCount="10"/>

<!-- 修改后 -->
<GameOption ServerListURL="http://你的服务器IP/tos/serverlist.xml" NewAccountURL="http://你的服务器IP:8011" PaymentURL="http://你的服务器IP:8011" LoadingImgURL="http://你的服务器IP/tos/loadingimg/" LoadingImgCount="10"/>
```

## 🔥 防火墙端口开放列表

### 核心游戏端口（必须开放）
| 端口 | 协议 | 服务 | 描述 |
|------|------|------|------|
| 80 | TCP | HTTP | Web服务器（PHPStudy Pro） |
| 2000 | TCP | Game | 游戏服务器端口 |
| 7001 | TCP | Game | 主服务器端口 |
| 8000 | TCP | Global | 全局服务器 |
| 8011 | TCP | Web | 账号/支付服务 |
| 8101 | TCP | Manager | 服务器管理端口 |
| 8102 | TCP | Node | Node管理器 |
| 9676 | TCP | Hub | Hub管理器 |
| 10001 | TCP | Statistics | 统计服务 |

### 数据库端口（内部使用，可选开放）
| 端口 | 协议 | 服务 | 描述 |
|------|------|------|------|
| 3306 | TCP | MySQL/MariaDB | 数据库服务 |
| 6379 | TCP | Redis | 缓存服务 |
| 17020 | TCP | MongoDB | 文档数据库 |

### 管理和监控端口（建议限制IP访问）
| 端口 | 协议 | 服务 | 描述 |
|------|------|------|------|
| 22 | TCP | SSH | 远程管理（Linux） |
| 3389 | TCP | RDP | 远程桌面（Windows） |

## 🛠️ 一键修改脚本

### Windows批处理脚本
```batch
@echo off
set /p SERVER_IP=请输入你的服务器IP地址: 

echo 正在修改配置文件...

:: 修改客户端配置
powershell -Command "(gc 'r1\data\xml_service\serverconfig.xml') -replace '127\.0\.0\.1', '%SERVER_IP%' | Out-File -encoding UTF8 'r1\data\xml_service\serverconfig.xml'"

:: 修改服务器列表
powershell -Command "(gc 'r1\server_release\serverlist.xml') -replace '127\.0\.0\.1', '%SERVER_IP%' | Out-File -encoding UTF8 'r1\server_release\serverlist.xml'"

:: 修改Web服务配置
powershell -Command "(gc 'r1\server_release\WebService\guild\exe\WebServices.exe.config') -replace '127\.0\.0\.1', '%SERVER_IP%' | Out-File -encoding UTF8 'r1\server_release\WebService\guild\exe\WebServices.exe.config'"

:: 修改机器配置
powershell -Command "(gc 'r1\data\xml_service\machine.xml') -replace 'L127\.0\.0\.1', 'L%SERVER_IP%' | Out-File -encoding UTF8 'r1\data\xml_service\machine.xml'"

:: 修改全局配置
powershell -Command "(gc 'r1\data\global\xml_service\serverconfig.xml') -replace '127\.0\.0\.1', '%SERVER_IP%' | Out-File -encoding UTF8 'r1\data\global\xml_service\serverconfig.xml'"

echo 配置文件修改完成！
echo 新的服务器IP: %SERVER_IP%
pause
```

### Linux Shell脚本
```bash
#!/bin/bash
read -p "请输入你的服务器IP地址: " SERVER_IP

echo "正在修改配置文件..."

# 修改客户端配置
sed -i "s/127\.0\.0\.1/$SERVER_IP/g" r1/data/xml_service/serverconfig.xml

# 修改服务器列表
sed -i "s/127\.0\.0\.1/$SERVER_IP/g" r1/server_release/serverlist.xml

# 修改Web服务配置
sed -i "s/127\.0\.0\.1/$SERVER_IP/g" r1/server_release/WebService/guild/exe/WebServices.exe.config

# 修改机器配置
sed -i "s/L127\.0\.0\.1/L$SERVER_IP/g" r1/data/xml_service/machine.xml

# 修改全局配置
sed -i "s/127\.0\.0\.1/$SERVER_IP/g" r1/data/global/xml_service/serverconfig.xml

echo "配置文件修改完成！"
echo "新的服务器IP: $SERVER_IP"
```

## 🔒 防火墙配置命令

### Windows防火墙配置
```batch
@echo off
echo 配置Windows防火墙规则...

:: 游戏核心端口
netsh advfirewall firewall add rule name="TOS-HTTP" dir=in action=allow protocol=TCP localport=80
netsh advfirewall firewall add rule name="TOS-Game-2000" dir=in action=allow protocol=TCP localport=2000
netsh advfirewall firewall add rule name="TOS-Game-7001" dir=in action=allow protocol=TCP localport=7001
netsh advfirewall firewall add rule name="TOS-Global-8000" dir=in action=allow protocol=TCP localport=8000
netsh advfirewall firewall add rule name="TOS-Web-8011" dir=in action=allow protocol=TCP localport=8011
netsh advfirewall firewall add rule name="TOS-Manager-8101" dir=in action=allow protocol=TCP localport=8101
netsh advfirewall firewall add rule name="TOS-Node-8102" dir=in action=allow protocol=TCP localport=8102
netsh advfirewall firewall add rule name="TOS-Hub-9676" dir=in action=allow protocol=TCP localport=9676
netsh advfirewall firewall add rule name="TOS-Stats-10001" dir=in action=allow protocol=TCP localport=10001

:: 数据库端口（可选）
netsh advfirewall firewall add rule name="MySQL" dir=in action=allow protocol=TCP localport=3306
netsh advfirewall firewall add rule name="Redis" dir=in action=allow protocol=TCP localport=6379
netsh advfirewall firewall add rule name="MongoDB" dir=in action=allow protocol=TCP localport=17020

echo 防火墙规则配置完成！
```

### Linux iptables配置
```bash
#!/bin/bash
echo "配置Linux防火墙规则..."

# 游戏核心端口
iptables -A INPUT -p tcp --dport 80 -j ACCEPT
iptables -A INPUT -p tcp --dport 2000 -j ACCEPT
iptables -A INPUT -p tcp --dport 7001 -j ACCEPT
iptables -A INPUT -p tcp --dport 8000 -j ACCEPT
iptables -A INPUT -p tcp --dport 8011 -j ACCEPT
iptables -A INPUT -p tcp --dport 8101 -j ACCEPT
iptables -A INPUT -p tcp --dport 8102 -j ACCEPT
iptables -A INPUT -p tcp --dport 9676 -j ACCEPT
iptables -A INPUT -p tcp --dport 10001 -j ACCEPT

# 数据库端口（可选）
iptables -A INPUT -p tcp --dport 3306 -j ACCEPT
iptables -A INPUT -p tcp --dport 6379 -j ACCEPT
iptables -A INPUT -p tcp --dport 17020 -j ACCEPT

# 保存规则
service iptables save

echo "防火墙规则配置完成！"
```

## ⚠️ 注意事项

1. **备份原始文件**：修改前请备份所有配置文件
2. **编码格式**：确保修改后的XML文件保持UTF-8编码
3. **端口冲突**：检查服务器上是否有其他服务占用这些端口
4. **安全考虑**：生产环境建议只开放必要的游戏端口，数据库端口不对外开放
5. **测试连接**：修改完成后使用 `telnet 你的服务器IP 端口号` 测试端口连通性

## 🚀 快速验证

修改完成后，可以使用以下命令验证端口是否正常监听：

### Windows
```cmd
netstat -an | findstr "端口号"
```

### Linux
```bash
netstat -tlnp | grep 端口号
```

完成以上配置后，你的TOS私服就可以通过外网IP访问了！