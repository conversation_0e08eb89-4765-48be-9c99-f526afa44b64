#
# Provide access to the documentation on your server as
#  http://yourserver.example.com/manual/
# The documentation is always available at
#  http://httpd.apache.org/docs/trunk/
#
# Required modules: mod_alias, mod_setenvif, mod_negotiation
#

AliasMatch ^/manual(?:/(?:de|en|es|fr|ja|ko|pt-br|ru|tr))?(/.*)?$ "${SRVROOT}/manual$1"

<Directory "${SRVROOT}/manual">
    Options Indexes
    AllowOverride None
    Require all granted

    <Files *.html>
        SetHandler type-map
    </Files>
    # .tr is text/troff in mime.types!
    <Files *.html.tr.utf8>
        ForceType text/html
    </Files>

    SetEnvIf Request_URI ^/manual/(de|en|es|fr|ja|ko|pt-br|ru|tr)/ prefer-language=$1
    RedirectMatch 301 ^/manual(?:/(de|en|es|fr|ja|ko|pt-br|ru|tr)){2,}(/.*)?$ /manual/$1$2

    LanguagePriority en de es fr ja ko pt-br ru tr
    ForceLanguagePriority Prefer Fallback
</Directory>
