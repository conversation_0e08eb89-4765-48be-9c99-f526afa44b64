#
# Virtual Hosts
#
# If you want to maintain multiple domains/hostnames on your
# machine you can setup VirtualHost containers for them. Most configurations
# use only name-based virtual hosts so the server doesn't need to worry about
# IP addresses. This is indicated by the asterisks in the directives below.
#
# Please see the documentation at 
# <URL:http://httpd.apache.org/docs/trunk/vhosts/>
# for further details before you try to setup virtual hosts.
#
# You may use the command line option '-S' to verify your virtual host
# configuration.

#
# Use name-based virtual hosting.
#

#
# VirtualHost example:
# Almost any Apache directive may go into a VirtualHost container.
# The first VirtualHost section is used for all requests that do not
# match a ServerName or ServerAlias in any <VirtualHost> block.
#

# NOTE 
# First Virtual Host MUST be a shallow duplicate of the main host 
# in httpd.conf. It will become the default host. Any request coming
# in that does not match any other VirtualHost's ServerName will be
# served by this host.

<VirtualHost _default_:80>
DocumentRoot "${SRVROOT}/htdocs"
#ServerName www.example.com:80
</VirtualHost>

# Add any other Virtual Hosts below

#<VirtualHost *:80>
#    ServerAdmin <EMAIL>
#    DocumentRoot "${SRVROOT}/docs/dummy-host.example.com"
#    ServerName dummy-host.example.com
#    ServerAlias www.dummy-host.example.com
#    ErrorLog "logs/dummy-host.example.com-error.log"
#    CustomLog "logs/dummy-host.example.com-access.log" common
#</VirtualHost>

#<VirtualHost *:80>
#    ServerAdmin <EMAIL>
#    DocumentRoot "${SRVROOT}/docs/dummy-host2.example.com"
#    ServerName dummy-host2.example.com
#    ErrorLog "logs/dummy-host2.example.com-error.log"
#    CustomLog "logs/dummy-host2.example.com-access.log" common
#</VirtualHost>



