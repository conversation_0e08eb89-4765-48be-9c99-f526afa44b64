#
# Distributed authoring and versioning (WebDAV)
#
# Required modules: mod_alias, mod_auth_digest, mod_authn_core, mod_authn_file,
#                   mod_authz_core, mod_authz_user, mod_dav, mod_dav_fs,
#                   mod_setenvif

# The following example gives DAV write access to a directory called
# "uploads" under the ServerRoot directory.
#
# The User/Group specified in httpd.conf needs to have write permissions
# on the directory where the DavLockDB is placed and on any directory where
# "Dav On" is specified.

DavLockDB "${SRVROOT}/var/DavLock"

Alias /uploads "${SRVROOT}/uploads"

<Directory "${SRVROOT}/uploads">
    Dav On

    AuthType Digest
    AuthName DAV-upload
    # You can use the htdigest program to create the password database:
    #   htdigest -c "${SRVROOT}/user.passwd" DAV-upload admin
    AuthUserFile "${SRVROOT}/user.passwd"
    AuthDigestProvider file

    # Allow universal read-access, but writes are restricted
    # to the admin user.
    <RequireAny>
        Require method GET POST OPTIONS
        Require user admin
    </RequireAny>
</Directory>

#
# The following directives disable redirects on non-GET requests for
# a directory that does not include the trailing slash.  This fixes a 
# problem with several clients that do not appropriately handle 
# redirects for folders with DAV methods.
#
BrowserMatch "Microsoft Data Access Internet Publishing Provider" redirect-carefully
BrowserMatch "MS FrontPage" redirect-carefully
BrowserMatch "^WebDrive" redirect-carefully
BrowserMatch "^WebDAVFS/1.[01234]" redirect-carefully
BrowserMatch "^gnome-vfs/1.0" redirect-carefully
BrowserMatch "^XML Spy" redirect-carefully
BrowserMatch "^Dreamweaver-WebDAV-SCM1" redirect-carefully
BrowserMatch " Konqueror/4" redirect-carefully
