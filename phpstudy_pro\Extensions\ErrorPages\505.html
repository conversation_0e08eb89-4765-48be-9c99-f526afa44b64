<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="utf-8">
  <title>505 错误 - phpstudy</title>
  <meta name="keywords" content="">
  <meta name="description" content="">
  <meta name="renderer" content="webkit">
  <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
  <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
  <meta name="apple-mobile-web-app-status-bar-style" content="black"> 
  <meta name="apple-mobile-web-app-capable" content="yes">
  <meta name="format-detection" content="telephone=no">
  <meta HTTP-EQUIV="pragma" CONTENT="no-cache"> 
  <meta HTTP-EQUIV="Cache-Control" CONTENT="no-store, must-revalidate"> 
  <meta HTTP-EQUIV="expires" CONTENT="Wed, 26 Feb 1997 08:21:57 GMT"> 
  <meta HTTP-EQUIV="expires" CONTENT="0">
  <style>
    body{
      font: 16px arial,'Microsoft Yahei','Hiragino Sans GB',sans-serif;
    }
    h1{
      margin: 0;
      color:#3a87ad;
      font-size: 26px;
    }
    .content{
      width: 45%;
      margin: 0 auto;
     
    }
    .content >div{
      margin-top: 200px;
      padding: 20px;
      background: #d9edf7;  
      border-radius: 12px;
    }
    .content dl{
      color: #2d6a88;
      line-height: 40px;
    } 
    .content div div {
      padding-bottom: 20px;
      text-align:center;
    }
  </style>
</head>
<body>
  <div class="content">
      <div>
           <h1>HTTP 505 - HTTP Version Not Supported</h1>
        <dl>
          <dt>错误说明：HTTP 版本不受支持。</dt>
          <dt>原因1：您的 Web 服务器不支持，或拒绝支持客户端（如您的浏览器）在发送给服务器的 HTTP 请求数据流中指定的 HTTP 协议版本</dt>
		  <dd>解决办法：</dd>
          <dd>升级您的 Web 服务器软件。</dd>
          <dt>原因2：http请求格式的错误</dt>
		  <dd>解决办法：</dd>
          <dd>对照一下自己的代码，从打印的信息中终于找到问题所在。可能在请求后面多加了一个空格。http协议真是很严格了。</dd>
        </dl>
        <div>使用手册，视频教程，BUG反馈，官网地址： <a href="https://www.xp.cn"  target="_blank">www.xp.cn</a> </div>
    
      </div>
    </div> 
</body>
</html>