PHP                                                                        NEWS
|||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||||
04 April 2019, PHP 7.3.4

- Core:
  . Fixed bug #77738 (Nullptr deref in zend_compile_expr). (Laruence)
  . Fixed bug #77660 (Segmentation fault on break 2147483648). (Laruence)
  . Fixed bug #77652 (Anonymous classes can lose their interface information).
    (<PERSON><PERSON>)
  . Fixed bug #77345 (Stack Overflow caused by circular reference in garbage
    collection). (<PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>)
  . Fixed bug #76956 (Wrong value for 'syslog.filter' documented in php.ini).
    (cmb)

- Apache2Handler:
  . Fixed bug #77648 (BOM in sapi/apache2handler/php_functions.c). (cmb)

- Bcmath:
  . Fixed bug #77742 (bcpow() implementation related to gcc compiler
    optimization). (<PERSON>ita)

- CLI Server:
  . Fixed bug #77722 (Incorrect IP set to $_SERVER['REMOTE_ADDR'] on the
    localhost). (<PERSON>ita)

- COM:
  . Fixed bug #77578 (Crash when php unload). (cmb)

- EXIF:
  . Fixed bug #77753 (Heap-buffer-overflow in php_ifd_get32s). (Stas)
  . Fixed bug #77831 (Heap-buffer-overflow in exif_iif_add_value). (Stas)

- FPM:
  . Fixed bug #77677 (FPM fails to build on AIX due to missing WCOREDUMP).
    (Kevin Adler)

- GD:
  . Fixed bug #77700 (Writing truecolor images as GIF ignores interlace flag).
    (cmb)

- MySQLi:
  . Fixed bug #77597 (mysqli_fetch_field hangs scripts). (Nikita)

- Opcache:
  . Fixed bug #77743 (Incorrect pi node insertion for jmpznz with identical
    successors). (Nikita)

- Phar:
  . Fxied bug #77697 (Crash on Big_Endian platform). (Laruence)

- phpdbg:
  . Fixed bug #77767 (phpdbg break cmd aliases listed in help do not match
    actual aliases). (Miriam Lauter)

- sodium:
  . Fixed bug #77646 (sign_detached() strings not terminated). (Frank)

- SQLite3:
  . Added sqlite3.defensive INI directive. (BohwaZ)

- Standard:
  . Fixed bug #77664 (Segmentation fault when using undefined constant in
    custom wrapper). (Laruence)
  . Fixed bug #77669 (Crash in extract() when overwriting extracted array).
    (Nikita)
  . Fixed bug #76717 (var_export() does not create a parsable value for
    PHP_INT_MIN). (Nikita)
  . Fixed bug #77765 (FTP stream wrapper should set the directory as
    executable). (Vlad Temian)

07 Mar 2019, PHP 7.3.3

- Core:
  . Fixed bug #77589 (Core dump using parse_ini_string with numeric sections).
    (Laruence)
  . Fixed bug #77329 (Buffer Overflow via overly long Error Messages).
    (Dmitry)
  . Fixed bug #77494 (Disabling class causes segfault on member access).
    (Dmitry)
  . Fixed bug #77498 (Custom extension Segmentation fault when declare static
    property). (Nikita)
  . Fixed bug #77530 (PHP crashes when parsing `(2)::class`). (Ekin)
  . Fixed bug #77546 (iptcembed broken function). (gdegoulet)
  . Fixed bug #77630 (rename() across the device may allow unwanted access
    during processing). (Stas)

- COM:
  . Fixed bug #77621 (Already defined constants are not properly reported).
    (cmb)
  . Fixed bug #77626 (Persistence confusion in php_com_import_typelib()). (cmb)

- EXIF:
  . Fixed bug #77509 (Uninitialized read in exif_process_IFD_in_TIFF). (Stas)
  . Fixed bug #77540 (Invalid Read on exif_process_SOFn). (Stas)
  . Fixed bug #77563 (Uninitialized read in exif_process_IFD_in_MAKERNOTE). (Stas)
  . Fixed bug #77659 (Uninitialized read in exif_process_IFD_in_MAKERNOTE). (Stas)

- Mbstring:
  . Fixed bug #77514 (mb_ereg_replace() with trailing backslash adds null byte).
    (Nikita)

- MySQL
  . Disabled LOCAL INFILE by default, can be enabled using php.ini directive
    mysqli.allow_local_infile for mysqli, or PDO::MYSQL_ATTR_LOCAL_INFILE
    attribute for pdo_mysql. (Darek Slusarczyk)

- OpenSSL:
  . Fixed bug #77390 (feof might hang on TLS streams in case of fragmented TLS
    records). (Abyl Valg, Jakub Zelenka)

- PDO_OCI:
  . Support Oracle Database tracing attributes ACTION, MODULE,
    CLIENT_INFO, and CLIENT_IDENTIFIER. (Cameron Porter)

- PHAR:
  . Fixed bug #77396 (Null Pointer Dereference in phar_create_or_parse_filename).
    (bishop)
  . Fixed bug #77586 (phar_tar_writeheaders_int() buffer overflow). (bishop)

- phpdbg:
  . Fixed bug #76596 (phpdbg support for display_errors=stderr). (kabel)

- SPL:
  . Fixed bug #51068 (DirectoryIterator glob:// don't support current path
    relative queries). (Ahmed Abdou)
  . Fixed bug #77431 (openFile() silently truncates after a null byte). (cmb)

- Standard:
  . Fixed bug #77552 (Unintialized php_stream_statbuf in stat functions).
    (John Stevenson)
  . Fixed bug #77612 (setcookie() sets incorrect SameSite header if all of its
    options filled). (Nikita)

07 Feb 2019, PHP 7.3.2

- Core:
  . Fixed bug #77369 (memcpy with negative length via crafted DNS response). (Stas)
  . Fixed bug #77387 (Recursion detection broken when printing GLOBALS).
    (Laruence)
  . Fixed bug #77376 ("undefined function" message no longer includes
    namespace). (Laruence)
  . Fixed bug #77357 (base64_encode / base64_decode doest not work on nested
    VM). (Nikita)
  . Fixed bug #77339 (__callStatic may get incorrect arguments). (Dmitry)
  . Fixed bug #77317 (__DIR__, __FILE__, realpath() reveal physical path for
    subst virtual drive). (Anatol)
  . Fixed bug #77263 (Segfault when using 2 RecursiveFilterIterator). (Dmitry)
  . Fixed bug #77447 (PHP 7.3 built with ASAN crashes in
    zend_cpu_supports_avx2). (Nikita)
  . Fixed bug #77484 (Zend engine crashes when calling realpath in invalid
    working dir). (Anatol)

- Curl:
  . Fixed bug #76675 (Segfault with H2 server push). (Pedro Magalhães)

- Fileinfo:
  . Fixed bug #77346 (webm files incorrectly detected as
    application/octet-stream). (Anatol)

- FPM:
  . Fixed bug #77430 (php-fpm crashes with Main process exited, code=dumped,
    status=11/SEGV). (Jakub Zelenka)

- GD:
  . Fixed bug #73281 (imagescale(…, IMG_BILINEAR_FIXED) can cause black border).
    (cmb)
  . Fixed bug #73614 (gdImageFilledArc() doesn't properly draw pies). (cmb)
  . Fixed bug #77272 (imagescale() may return image resource on failure). (cmb)
  . Fixed bug #77391 (1bpp BMPs may fail to be loaded). (Romain Déoux, cmb)
  . Fixed bug #77479 (imagewbmp() segfaults with very large images). (cmb)

- ldap:
  . Fixed bug #77440 (ldap_bind using ldaps or ldap_start_tls()=exception in
    libcrypto-1_1-x64.dll). (Anatol)

- Mbstring:
  . Fixed bug #77428 (mb_ereg_replace() doesn't replace a substitution
    variable). (Nikita)
  . Fixed bug #77454 (mb_scrub() silently truncates after a null byte).
    (64796c6e69 at gmail dot com)

- MySQLnd:
  . Fixed bug #77308 (Unbuffered queries memory leak). (Dmitry)
  . Fixed bug #75684 (In mysqlnd_ext_plugin.h the plugin methods family has
      no external visibility). (Anatol)

- Opcache:
  . Fixed bug #77266 (Assertion failed in dce_live_ranges). (Laruence)
  . Fixed bug #77257 (value of variable assigned in a switch() construct gets
    lost). (Nikita)
  . Fixed bug #77434 (php-fpm workers are segfaulting in zend_gc_addre).
    (Nikita)
  . Fixed bug #77361 (configure fails on 64-bit AIX when opcache enabled).
    (Kevin Adler)
  . Fixed bug #77287 (Opcache literal compaction is incompatible with EXT
    opcodes). (Nikita)

- PCRE:
  . Fixed bug #77338 (get_browser with empty string). (Nikita)

- PDO:
  . Fixed bug #77273 (array_walk_recursive corrupts value types leading to PDO
    failure). (Nikita)

- PDO MySQL:
  . Fixed bug #77289 (PDO MySQL segfaults with persistent connection).
    (Lauri Kenttä)

- SOAP:
  . Fixed bug #77410 (Segmentation Fault when executing method with an empty
    parameter). (Nikita)

- Sockets:
  . Fixed bug #76839 (socket_recvfrom may return an invalid 'from' address
    on MacOS). (Michael Meyer)

- SPL:
  . Fixed bug #77298 (segfault occurs when add property to unserialized empty
    ArrayObject). (jhdxr)

- Standard:
  . Fixed bug #77395 (segfault about array_multisort). (Laruence)
  . Fixed bug #77439 (parse_str segfaults when inserting item into existing
    array). (Nikita)

10 Jan 2019, PHP 7.3.1

- Core:
  . Fixed bug #76654 (Build failure on Mac OS X on 32-bit Intel). (Ryandesign)
  . Fixed bug #71041 (zend_signal_startup() needs ZEND_API).
    (Valentin V. Bartenev)
  . Fixed bug #76046 (PHP generates "FE_FREE" opcode on the wrong line).
    (Nikita)
  . Fixed bug #77291 (magic methods inherited from a trait may be ignored).
    (cmb)

- CURL:
  . Fixed bug #77264 (curl_getinfo returning microseconds, not seconds).
    (Pierrick)

- COM:
  . Fixed bug #77177 (Serializing or unserializing COM objects crashes). (cmb)

- Exif:
  . Fixed bug #77184 (Unsigned rational numbers are written out as signed
    rationals). (Colin Basnett)

- GD:
  . Fixed bug #77195 (Incorrect error handling of imagecreatefromjpeg()). (cmb)
  . Fixed bug #77198 (auto cropping has insufficient precision). (cmb)
  . Fixed bug #77200 (imagecropauto(…, GD_CROP_SIDES) crops left but not right).
    (cmb)
  . Fixed bug #77269 (efree() on uninitialized Heap data in imagescale leads to
    use-after-free). (cmb)
  . Fixed bug #77270 (imagecolormatch Out Of Bounds Write on Heap). (cmb)

- MBString:
  . Fixed bug #77367 (Negative size parameter in mb_split). (Stas)
  . Fixed bug #77370 (Buffer overflow on mb regex functions - fetch_token).
    (Stas)
  . Fixed bug #77371 (heap buffer overflow in mb regex functions
    - compile_string_node). (Stas)
  . Fixed bug #77381 (heap buffer overflow in multibyte match_at). (Stas)
  . Fixed bug #77382 (heap buffer overflow due to incorrect length in
    expand_case_fold_string). (Stas)
  . Fixed bug #77385 (buffer overflow in fetch_token). (Stas)
  . Fixed bug #77394 (Buffer overflow in multibyte case folding - unicode).
    (Stas)
  . Fixed bug #77418 (Heap overflow in utf32be_mbc_to_code). (Stas)

- OCI8:
  . Fixed bug #76804 (oci_pconnect with OCI_CRED_EXT not working). (KoenigsKind)
  . Added oci_set_call_timeout() for call timeouts.
  . Added oci_set_db_operation() for the DBOP end-to-end-tracing attribute.

- Opcache:
  . Fixed bug #77215 (CFG assertion failure on multiple finalizing switch
    frees in one block). (Nikita)
  . Fixed bug #77275 (OPcache optimization problem for ArrayAccess->offsetGet).
    (Nikita)

- PCRE:
  . Fixed bug #77193 (Infinite loop in preg_replace_callback). (Anatol)

- PDO:
  . Handle invalid index passed to PDOStatement::fetchColumn() as error. (Sergei
    Morozov)

- Phar:
  . Fixed bug #77247 (heap buffer overflow in phar_detect_phar_fname_ext). (Stas)

- Soap:
  . Fixed bug #77088 (Segfault when using SoapClient with null options).
    (Laruence)

- Sockets:
  . Fixed bug #77136 (Unsupported IPV6_RECVPKTINFO constants on macOS).
    (Mizunashi Mana)

- Sodium:
  . Fixed bug #77297 (SodiumException segfaults on PHP 7.3). (Nikita, Scott)

- SPL:
  . Fixed bug #77359 (spl_autoload causes segfault). (Lauri Kenttä)
  . Fixed bug #77360 (class_uses causes segfault). (Lauri Kenttä)

- SQLite3:
  . Fixed bug #77051 (Issue with re-binding on SQLite3). (BohwaZ)

- Xmlrpc:
  . Fixed bug #77242 (heap out of bounds read in xmlrpc_decode()). (cmb)
  . Fixed bug #77380 (Global out of bounds read in xmlrpc base64 code). (Stas)

06 Dec 2018, PHP 7.3.0

- Core:
  . Improved PHP GC. (Dmitry, Nikita)
  . Redesigned the old ext_skel program written in PHP, run:
    'php ext_skel.php' for all options. This means there are no dependencies,
    thus making it work on Windows out of the box. (Kalle)
  . Removed support for BeOS. (Kalle)
  . Add PHP_VERSION to phpinfo() <title/>. (github/MattJeevas)
  . Add net_get_interfaces(). (Sara, Joe, Anatol)
  . Added gc_status(). (Benjamin Eberlei)
  . Implemented flexible heredoc and nowdoc syntax, per
    RFC https://wiki.php.net/rfc/flexible_heredoc_nowdoc_syntaxes.
    (Thomas Punt)
  . Added support for references in list() and array destructuring, per
    RFC https://wiki.php.net/rfc/list_reference_assignment.
    (David Walker)
  . Improved effectiveness of ZEND_SECURE_ZERO for NetBSD and systems
    without native similar feature. (devnexen)
  . Added syslog.facility and syslog.ident INI entries for customizing syslog
    logging. (Philip Prindeville)
  . Fixed bug #75683 (Memory leak in zend_register_functions() in ZTS mode).
    (Dmitry)
  . Fixed bug #75031 (support append mode in temp/memory streams). (adsr)
  . Fixed bug #74860 (Uncaught exceptions not being formatted properly when
    error_log set to "syslog"). (Philip Prindeville)
  . Fixed bug #75220 (Segfault when calling is_callable on parent).
    (andrewnester)
  . Fixed bug #69954 (broken links and unused config items in distributed ini
    files). (petk)
  . Fixed bug #74922 (Composed class has fatal error with duplicate, equal const
    properties). (pmmaga)
  . Fixed bug #63911 (identical trait methods raise errors during composition).
    (pmmaga)
  . Fixed bug #75677 (Clang ignores fastcall calling convention on variadic
    function). (Li-Wen Hsu)
  . Fixed bug #54043 (Remove inconsitency of internal exceptions and user
    defined exceptions). (Nikita)
  . Fixed bug #53033 (Mathematical operations convert objects to integers).
    (Nikita)
  . Fixed bug #73108 (Internal class cast handler uses integer instead of
    float). (Nikita)
  . Fixed bug #75765 (Fatal error instead of Error exception when base class is
    not found). (Timur Ibragimov)
  . Fixed bug #76198 (Wording: "iterable" is not a scalar type). (Levi Morrison)
  . Fixed bug #76137 (config.guess/config.sub do not recognize RISC-V). (cmb)
  . Fixed bug #76427 (Segfault in zend_objects_store_put). (Laruence)
  . Fixed bug #76422 (ftruncate fails on files > 2GB). (Anatol)
  . Fixed bug #76509 (Inherited static properties can be desynchronized from
    their parent by ref). (Nikita)
  . Fixed bug #76439 (Changed behaviour in unclosed HereDoc). (Nikita, tpunt)
  . Fixed bug #63217 (Constant numeric strings become integers when used as
    ArrayAccess offset). (Rudi Theunissen, Dmitry)
  . Fixed bug #33502 (Some nullary functions don't check the number of
    arguments). (cmb)
  . Fixed bug #76392 (Error relocating sapi/cli/php: unsupported relocation
    type 37). (Peter Kokot)
  . The declaration and use of case-insensitive constants has been deprecated.
    (Nikita)
  . Added syslog.filter INI entry for syslog filtering. (Philip Prindeville)
  . Fixed bug #76667 (Segfault with divide-assign op and __get + __set).
    (Laruence)
  . Fixed bug #76030 (RE2C_FLAGS rarely honoured) (Cristian Rodríguez)
  . Fixed broken zend_read_static_property (Laruence)
  . Fixed bug #76773 (Traits used on the parent are ignored for child classes).
    (daverandom)
  . Fixed bug #76767 (‘asm’ operand has impossible constraints in zend_operators.h).
    (ondrej)
  . Fixed bug #76752 (Crash in ZEND_COALESCE_SPEC_TMP_HANDLER - assertion in
    _get_zval_ptr_tmp failed). (Laruence)
  . Fixed bug #76820 (Z_COPYABLE invalid definition). (mvdwerve, cmb)
  . Fixed bug #76510 (file_exists() stopped working for phar://). (cmb)
  . Fixed bug #76869 (Incorrect bypassing protected method accessibilty check).
    (Dmitry)
  . Fixed bug #72635 (Undefined class used by class constant in constexpr
    generates fatal error). (Nikita)
  . Fixed bug #76947 (file_put_contents() blocks the directory of the file
    (__DIR__)). (Anatol)
  . Fixed bug #76979 (define() error message does not mention resources as
    valid values). (Michael Moravec)
  . Fixed bug #76825 (Undefined symbols ___cpuid_count). (Laruence, cmb)
  . Fixed bug #77110 (undefined symbol zend_string_equal_val in C++ build).
    (Remi)

- BCMath:
  . Implemented FR #67855 (No way to get current scale in use). (Chris Wright,
    cmb)
  . Fixed bug #66364 (BCMath bcmul ignores scale parameter). (cmb)
  . Fixed bug #75164 (split_bc_num() is pointless). (cmb)
  . Fixed bug #75169 (BCMath errors/warnings bypass PHP's error handling). (cmb)

- CLI:
  . Fixed bug #44217 (Output after stdout/stderr closed cause immediate exit
    with status 0). (Robert Lu)
  . Fixed bug #77111 (php-win.exe corrupts unicode symbols from cli
    parameters). (Anatol)

- cURL:
  . Expose curl constants from curl 7.50 to 7.61. (Pierrick)
  . Fixed bug #74125 (Fixed finding CURL on systems with multiarch support).
    (cebe)

- Date:
  . Implemented FR #74668: Add DateTime::createFromImmutable() method.
    (majkl578, Rican7)
  . Fixed bug #75222 (DateInterval microseconds property always 0). (jhdxr)
  . Fixed bug #68406 (calling var_dump on a DateTimeZone object modifies it).
    (jhdxr)
  . Fixed bug #76131 (mismatch arginfo for date_create). (carusogabriel)
  . Updated timelib to 2018.01RC1 to address several bugs:
    . Fixed bug #75577 (DateTime::createFromFormat does not accept 'v' format
      specifier). (Derick)
    . Fixed bug #75642 (Wrap around behaviour for microseconds is not working).
      (Derick)

- DBA:
  . Fixed bug #75264 (compiler warnings emitted). (petk)

- DOM:
  . Fixed bug #76285 (DOMDocument::formatOutput attribute sometimes ignored).
    (Andrew Nester, Laruence, Anatol)

- Fileinfo:
  . Fixed bug #77095 (slowness regression in 7.2/7.3 (compared to 7.1)).
    (Anatol)

- Filter:
  . Added the 'add_slashes' sanitization mode (FILTER_SANITIZE_ADD_SLASHES).
	(Kalle)

- FPM:
  . Added fpm_get_status function. (Till Backhaus)
  . Fixed bug #62596 (getallheaders() missing with PHP-FPM). (Remi)
  . Fixed bug #69031 (Long messages into stdout/stderr are truncated
    incorrectly) - added new log related FPM configuration options:
    log_limit, log_buffering and decorate_workers_output. (Jakub Zelenka)

- ftp:
  . Fixed bug #77151 (ftp_close(): SSL_read on shutdown). (Remi)

- GD:
  . Added support for WebP in imagecreatefromstring(). (Andreas Treichel, cmb)

- GMP:
  . Export internal structures and accessor helpers for GMP object. (Sara)
  . Added gmp_binomial(n, k). (Nikita)
  . Added gmp_lcm(a, b). (Nikita)
  . Added gmp_perfect_power(a). (Nikita)
  . Added gmp_kronecker(a, b). (Nikita)

- iconv:
  . Fixed bug #53891 (iconv_mime_encode() fails to Q-encode UTF-8 string). (cmb)
  . Fixed bug #77147 (Fixing 60494 ignored ICONV_MIME_DECODE_CONTINUE_ON_ERROR).
    (cmb)

- IMAP:
  . Fixed bug #77020 (null pointer dereference in imap_mail). (cmb)
  . Fixed bug #77153 (imap_open allows to run arbitrary shell commands via
    mailbox parameter). (Stas)

- Interbase:
  . Fixed bug #75453 (Incorrect reflection for ibase_[p]connect). (villfa)
  . Fixed bug #76443 (php+php_interbase.dll crash on module_shutdown). (Kalle)


- intl:
  . Fixed bug #75317 (UConverter::setDestinationEncoding changes source instead
    of destination). (andrewnester)
  . Fixed bug #76829 (Incorrect validation of domain on idn_to_utf8()
    function). (Anatol)

- JSON:
  . Added JSON_THROW_ON_ERROR flag. (Andrea)

- LDAP:
  . Added ldap_exop_refresh helper for EXOP REFRESH operation with dds overlay.
    (Come)
  . Added full support for sending and parsing ldap controls. (Come)
  . Fixed bug #49876 (Fix LDAP path lookup on 64-bit distros). (dzuelke)

- libxml2:
  . Fixed bug #75871 (use pkg-config where available). (pmmaga)

- litespeed:
  . Fixed bug #75248 (Binary directory doesn't get created when building
    only litespeed SAPI). (petk)
  . Fixed bug #75251 (Missing program prefix and suffix). (petk)

- MBstring:
  . Updated to Oniguruma 6.9.0. (cmb)
  . Fixed bug #65544 (mb title case conversion-first word in quotation isn't
    capitalized). (Nikita)
  . Fixed bug #71298 (MB_CASE_TITLE misbehaves with curled apostrophe/quote).
    (Nikita)
  . Fixed bug #73528 (Crash in zif_mb_send_mail). (Nikita)
  . Fixed bug #74929 (mbstring functions version 7.1.1 are slow compared to 5.3
    on Windows). (Nikita)
  . Fixed bug #76319 (mb_strtolower with invalid UTF-8 causes segmentation
    fault). (Nikita)
  . Fixed bug #76574 (use of undeclared identifiers INT_MAX and LONG_MAX). (cmb)
  . Fixed bug #76594 (Bus Error due to unaligned access in zend_ini.c
    OnUpdateLong). (cmb, Nikita)
  . Fixed bug #76706 (mbstring.http_output_conv_mimetypes is ignored). (cmb)
  . Fixed bug #76958 (Broken UTF7-IMAP conversion). (Nikita)
  . Fixed bug #77025 (mb_strpos throws Unknown encoding or conversion error).
    (Nikita)
  . Fixed bug #77165 (mb_check_encoding crashes when argument given an empty
    array). (Nikita)

- Mysqlnd:
  . Fixed bug #76386 (Prepared Statement formatter truncates fractional seconds
    from date/time column). (Victor Csiky)

- ODBC:
  . Removed support for ODBCRouter. (Kalle)
  . Removed support for Birdstep. (Kalle)
  . Fixed bug #77079 (odbc_fetch_object has incorrect type signature).
    (Jon Allen)

- Opcache:
  . Fixed bug #76466 (Loop variable confusion). (Dmitry, Laruence, Nikita)
  . Fixed bug #76463 (var has array key type but not value type). (Laruence)
  . Fixed bug #76446 (zend_variables.c:73: zend_string_destroy: Assertion
    `!(zval_gc_flags((str)->gc)). (Nikita, Laruence)
  . Fixed bug #76711 (OPcache enabled triggers false-positive "Illegal string
    offset"). (Dmitry)
  . Fixed bug #77058 (Type inference in opcache causes side effects). (Nikita)
  . Fixed bug #77092 (array_diff_key() - segmentation fault). (Nikita)

- OpenSSL:
  . Added openssl_pkey_derive function. (Jim Zubov)
  . Add min_proto_version and max_proto_version ssl stream options as well as
    related constants for possible TLS protocol values. (Jakub Zelenka)

- PCRE:
  . Implemented https://wiki.php.net/rfc/pcre2-migration. (Anatol, Dmitry)
  . Upgrade PCRE2 to 10.32. (Anatol)
  . Fixed bug #75355 (preg_quote() does not quote # control character).
    (Michael Moravec)
  . Fixed bug #76512 (\w no longer includes unicode characters). (cmb)
  . Fixed bug #76514 (Regression in preg_match makes it fail with
    PREG_JIT_STACKLIMIT_ERROR). (Anatol)
  . Fixed bug #76909 (preg_match difference between 7.3 and < 7.3). (Anatol)

- PDO_DBlib:
  . Implemented FR #69592 (allow 0-column rowsets to be skipped automatically).
    (fandrieu)
  . Expose TDS version as \PDO::DBLIB_ATTR_TDS_VERSION attribute on \PDO
    instance. (fandrieu)
  . Treat DATETIME2 columns like DATETIME. (fandrieu)
  . Fixed bug #74243 (allow locales.conf to drive datetime format). (fandrieu)

- PDO_Firebird:
  . Fixed bug #74462 (PDO_Firebird returns only NULLs for results with boolean
    for FIREBIRD >= 3.0). (Dorin Marcoci)

- PDO_OCI:
  . Fixed bug #74631 (PDO_PCO with PHP-FPM: OCI environment initialized
    before PHP-FPM sets it up). (Ingmar Runge)

- PDO SQLite
  . Add support for additional open flags

- pgsql:
  . Added new error constants for pg_result_error(): PGSQL_DIAG_SCHEMA_NAME,
    PGSQL_DIAG_TABLE_NAME, PGSQL_DIAG_COLUMN_NAME, PGSQL_DIAG_DATATYPE_NAME,
    PGSQL_DIAG_CONSTRAINT_NAME and PGSQL_DIAG_SEVERITY_NONLOCALIZED. (Kalle) 
  . Fixed bug #77047 (pg_convert has a broken regex for the 'TIME WITHOUT
    TIMEZONE' data type). (Andy Gajetzki)

- phar:
  . Fixed bug #74991 (include_path has a 4096 char limit in some cases).
    (bwbroersma)
  . Fixed bug #65414 (deal with leading slash when adding files correctly).
    (bishopb)

- readline:
  . Added completion_append_character and completion_suppress_append options
    to readline_info() if linked against libreadline. (krageon)

- Session:
  . Fixed bug #74941 (session fails to start after having headers sent).
    (morozov)

- SimpleXML:
  . Fixed bug #54973 (SimpleXML casts integers wrong). (Nikita)
  . Fixed bug #76712 (Assignment of empty string creates extraneous text node).
    (cmb)

- Sockets:
  . Fixed bug #67619 (Validate length on socket_write). (thiagooak)

- SOAP:
  . Fixed bug #75464 (Wrong reflection on SoapClient::__setSoapHeaders).
    (villfa)
  . Fixed bug #70469 (SoapClient generates E_ERROR even if exceptions=1 is
    used). (Anton Artamonov)
  . Fixed bug #50675 (SoapClient can't handle object references correctly).
    (Cameron Porter)
  . Fixed bug #76348 (WSDL_CACHE_MEMORY causes Segmentation fault). (cmb)
  . Fixed bug #77141 (Signedness issue in SOAP when precision=-1). (cmb)

- SPL:
  . Fixed bug #74977 (Appending AppendIterator leads to segfault).
    (Andrew Nester)
  . Fixed bug #75173 (incorrect behavior of AppendIterator::append in foreach
    loop). (jhdxr)
  . Fixed bug #74372 (autoloading file with syntax error uses next autoloader,
    may hide parse error). (Nikita)
  . Fixed bug #75878 (RecursiveTreeIterator::setPostfix has wrong signature).
    (cmb)
  . Fixed bug #74519 (strange behavior of AppendIterator). (jhdxr)
  . Fixed bug #76131 (mismatch arginfo for splarray constructor).
    (carusogabriel)

- SQLite3:
  . Updated bundled libsqlite to 3.24.0. (cmb)

- Standard:
  . Added is_countable() function. (Gabriel Caruso)
  . Added support for the SameSite cookie directive, including an alternative
    signature for setcookie(), setrawcookie() and session_set_cookie_params().
    (Frederik Bosch, pmmaga)
  . Remove superfluous warnings from inet_ntop()/inet_pton(). (daverandom)
  . Fixed bug #75916 (DNS_CAA record results contain garbage). (Mike,
    Philip Sharp)
  . Fixed unserialize(), to disable creation of unsupported data structures
    through manually crafted strings. (Dmitry)
  . Fixed bug #75409 (accept EFAULT in addition to ENOSYS as indicator
    that getrandom() is missing). (sarciszewski)
  . Fixed bug #74719 (fopen() should accept NULL as context). (Alexander Holman)
  . Fixed bug #69948 (path/domain are not sanitized in setcookie). (cmb)
  . Fixed bug #75996 (incorrect url in header for mt_rand). (tatarbj)
  . Added hrtime() function, to get high resolution time. (welting)
  . Fixed bug #48016 (stdClass::__setState is not defined although var_export()
    uses it). (Andrea)
  . Fixed bug #76136 (stream_socket_get_name should enclose IPv6 in brackets).
    (seliver)
  . Fixed bug #76688 (Disallow excessive parameters after options array).
    (pmmaga)
  . Fixed bug #76713 (Segmentation fault caused by property corruption).
    (Laruence)
  . Fixed bug #76755 (setcookie does not accept "double" type for expire time).
    (Laruence)
  . Fixed bug #76674 (improve array_* failure messages exposing what was passed
    instead of an array). (carusogabriel)
  . Fixed bug #76803 (ftruncate changes file pointer). (Anatol)
  . Fixed bug #76818 (Memory corruption and segfault). (Remi)
  . Fixed bug #77081 (ftruncate() changes seek pointer in c mode). (cmb, Anatol)

- Testing:
  . Implemented FR #62055 (Make run-tests.php support --CGI-- sections). (cmb)

- Tidy:
  . Support using tidyp instead of tidy. (devnexen)
  . Fixed bug #74707 (Tidy has incorrect ReflectionFunction param counts for
    functions taking tidy). (Gabriel Caruso)
  . Fixed arginfo for tidy::__construct(). (Tyson Andre)

- Tokenizer:
  . Fixed bug #76437 (token_get_all with TOKEN_PARSE flag fails to recognise
    close tag). (Laruence)
  . Fixed bug #75218 (Change remaining uncatchable fatal errors for parsing
    into ParseError). (Nikita)
  . Fixed bug #76538 (token_get_all with TOKEN_PARSE flag fails to recognise
    close tag with newline). (Nikita)
  . Fixed bug #76991 (Incorrect tokenization of multiple invalid flexible
    heredoc strings). (Nikita)

- XML:
  . Fixed bug #71592 (External entity processing never fails). (cmb)

- Zlib:
  . Added zlib/level context option for compress.zlib wrapper. (Sara)

08 Nov 2018, PHP 7.2.12

- Core:
  . Fixed bug #76846 (Segfault in shutdown function after memory limit error).
    (Nikita)
  . Fixed bug #76946 (Cyclic reference in generator not detected). (Nikita)
  . Fixed bug #77035 (The phpize and ./configure create redundant .deps file).
    (Peter Kokot)
  . Fixed bug #77041 (buildconf should output error messages to stderr)
    (Mizunashi Mana)

- Date:
  . Upgraded timelib to 2017.08. (Derick)
  . Fixed bug #75851 (Year component overflow with date formats "c", "o", "r"
    and "y"). (Adam Saponara)
  . Fixed bug #77007 (fractions in `diff()` are not correctly normalized).
    (Derick)

- FCGI:
  . Fixed #76948 (Failed shutdown/reboot or end session in Windows). (Anatol)
  . Fixed bug #76954 (apache_response_headers removes last character from header
    name). (stodorovic)

- FTP:
  . Fixed bug #76972 (Data truncation due to forceful ssl socket shutdown).
    (Manuel Mausz)

- intl:
  . Fixed bug #76942 (U_ARGUMENT_TYPE_MISMATCH). (anthrax at unixuser dot org)

- Reflection:
  . Fixed bug #76936 (Objects cannot access their private attributes while
    handling reflection errors). (Nikita)
  . Fixed bug #66430 (ReflectionFunction::invoke does not invoke closure with
    object scope). (Nikita)

- Sodium:
  . Some base64 outputs were truncated; this is not the case any more.
    (jedisct1)
  . block sizes >= 256 bytes are now supposed by sodium_pad() even
    when an old version of libsodium has been installed. (jedisct1)
  . Fixed bug #77008 (sodium_pad() could read (but not return nor write)
    uninitialized memory when trying to pad an empty input). (jedisct1)

- Standard:
  . Fixed bug #76965 (INI_SCANNER_RAW doesn't strip trailing whitespace).
    (Pierrick)

- Tidy:
  . Fixed bug #77027 (tidy::getOptDoc() not available on Windows). (cmb)

- XML:
  . Fixed bug #30875 (xml_parse_into_struct() does not resolve entities). (cmb)
  . Add support for getting SKIP_TAGSTART and SKIP_WHITE options. (cmb)

- XMLRPC:
  . Fixed bug #75282 (xmlrpc_encode_request() crashes). (cmb)

11 Oct 2018, PHP 7.2.11

- Core:
  . Fixed bug #76800 (foreach inconsistent if array modified during loop).
    (Dmitry)
  . Fixed bug #76901 (method_exists on SPL iterator passthrough method corrupts
    memory). (Nikita)

- CURL:
  . Fixed bug #76480 (Use curl_multi_wait() so that timeouts are respected).
    (Pierrick)

- iconv:
  . Fixed bug #66828 (iconv_mime_encode Q-encoding longer than it should be).
    (cmb)

- Opcache:
  . Fixed bug #76832 (ZendOPcache.MemoryBase periodically deleted by the OS).
    (Anatol)
  . Fixed bug #76796 (Compile-time evaluation of disabled function in opcache
    causes segfault). (Nikita)

- POSIX:
  . Fixed bug #75696 (posix_getgrnam fails to print details of group). (cmb)

- Reflection:
  . Fixed bug #74454 (Wrong exception being thrown when using ReflectionMethod).
    (cmb)

- Standard:
  . Fixed bug #73457 (Wrong error message when fopen FTP wrapped fails to open
    data connection). (Ville Hukkamäki)
  . Fixed bug #74764 (Bindto IPv6 works with file_get_contents but fails with
    stream_socket_client). (Ville Hukkamäki)
  . Fixed bug #75533 (array_reduce is slow when $carry is large array).
    (Manabu Matsui)

- XMLRPC:
  . Fixed bug #76886 (Can't build xmlrpc with expat). (Thomas Petazzoni, cmb)

- Zlib:
  . Fixed bug #75273 (php_zlib_inflate_filter() may not update bytes_consumed).
    (Martin Burke, cmb)

13 Sep 2018, PHP 7.2.10

- Core:
  . Fixed bug #76754 (parent private constant in extends class memory leak).
    (Laruence)
  . Fixed bug #72443 (Generate enabled extension). (petk)
  . Fixed bug #75797 (Memory leak when using class_alias() in non-debug mode).
    (Massimiliano Braglia)

- Apache2:
  . Fixed bug #76582 (Apache bucket brigade sometimes becomes invalid). (stas)

- Bz2:
  . Fixed arginfo for bzcompress. (Tyson Andre)

- gettext:
  . Fixed bug #76517 (incorrect restoring of LDFLAGS). (sji)

- iconv:
  . Fixed bug #68180 (iconv_mime_decode can return extra characters in a
    header). (cmb)
  . Fixed bug #63839 (iconv_mime_decode_headers function is skipping headers).
    (cmb)
  . Fixed bug #60494 (iconv_mime_decode does ignore special characters). (cmb)
  . Fixed bug #55146 (iconv_mime_decode_headers() skips some headers). (cmb)

- intl:
  . Fixed bug #74484 (MessageFormatter::formatMessage memory corruption with
    11+ named placeholders). (Anatol)

- libxml:
  . Fixed bug #76777 ("public id" parameter of libxml_set_external_entity_loader
    callback undefined). (Ville Hukkamäki)

- mbstring:
  . Fixed bug #76704 (mb_detect_order return value varies based on argument
    type). (cmb)

- Opcache:
  . Fixed bug #76747 (Opcache treats path containing "test.pharma.tld" as a phar
    file). (Laruence)

- OpenSSL:
  . Fixed bug #76705 (unusable ssl => peer_fingerprint in
    stream_context_create()). (Jakub Zelenka)

- phpdbg:
  . Fixed bug #76595 (phpdbg man page contains outdated information).
    (Kevin Abel)

- SPL:
  . Fixed bug #68825 (Exception in DirectoryIterator::getLinkTarget()). (cmb)
  . Fixed bug #68175 (RegexIterator pregFlags are NULL instead of 0). (Tim
    Siebels)

- Standard:
  . Fixed bug #76778 (array_reduce leaks memory if callback throws exception).
    (cmb)

- zlib:
  . Fixed bug #65988 (Zlib version check fails when an include/zlib/ style dir
    is passed to the --with-zlib configure option). (Jay Bonci)
  . Fixed bug #76709 (Minimal required zlib library is 1.2.0.4). (petk)

16 Aug 2018, PHP 7.2.9

- Calendar:
  . Fixed bug #52974 (jewish.c: compile error under Windows with GBK charset).
    (cmb)

- Filter:
  . Fixed bug #76366 (References in sub-array for filtering breaks the filter).
    (ZiHang Gao)

- PDO_Firebird:
  . Fixed bug #76488 (Memory leak when fetching a BLOB field). (Simonov Denis)

- PDO_PgSQL:
  . Fixed bug #75402 (Possible Memory Leak using PDO::CURSOR_SCROLL option).
    (Anatol)

- SQLite3:
  . Fixed #76665 (SQLite3Stmt::bindValue() with SQLITE3_FLOAT doesn't juggle).
    (cmb)

- Standard:
  . Fixed bug #73817 (Incorrect entries in get_html_translation_table). (cmb)
  . Fixed bug #68553 (array_column: null values in $index_key become incrementing
    keys in result). (Laruence)
  . Fixed bug #76643 (Segmentation fault when using `output_add_rewrite_var`).
    (cmb)

- Zip:
  . Fixed bug #76524 (ZipArchive memory leak (OVERWRITE flag and empty archive)).
    (Timur Ibragimov)

19 Jul 2018, PHP 7.2.8

- Core:
  . Fixed bug #76534 (PHP hangs on 'illegal string offset on string references
    with an error handler). (Laruence)
  . Fixed bug #76520 (Object creation leaks memory when executed over HTTP).
    (Nikita)
  . Fixed bug #76502 (Chain of mixed exceptions and errors does not serialize
    properly). (Nikita)

- Date:
  . Fixed bug #76462 (Undefined property: DateInterval::$f). (Anatol)

- EXIF:
  . Fixed bug #76409 (heap use after free in _php_stream_free). (cmb)
  . Fixed bug #76423 (Int Overflow lead to Heap OverFlow in
    exif_thumbnail_extract of exif.c). (Stas)
  . Fixed bug #76557 (heap-buffer-overflow (READ of size 48) while reading exif
    data). (Stas)

- FPM:
  . Fixed bug #73342 (Vulnerability in php-fpm by changing stdin to
    non-blocking). (Nikita)

- GMP:
  . Fixed bug #74670 (Integer Underflow when unserializing GMP and possible
    other classes). (Nikita)

- intl:
  . Fixed bug #76556 (get_debug_info handler for BreakIterator shows wrong
    type). (cmb)

- mbstring:
  . Fixed bug #76532 (Integer overflow and excessive memory usage
    in mb_strimwidth). (MarcusSchwarz)

- Opcache:
  . Fixed bug #76477 (Opcache causes empty return value).
    (Nikita, Laruence)

- PGSQL:
  . Fixed bug #76548 (pg_fetch_result did not fetch the next row). (Anatol)

- phpdbg:
  . Fix arginfo wrt. optional/required parameters. (cmb)

- Reflection:
  . Fixed bug #76536 (PHP crashes with core dump when throwing exception in
    error handler). (Laruence)
  . Fixed bug #75231 (ReflectionProperty#getValue() incorrectly works with
    inherited classes). (Nikita)

- Standard:
  . Fixed bug #76505 (array_merge_recursive() is duplicating sub-array keys).
    (Laruence)
  . Fixed bug #71848 (getimagesize with $imageinfo returns false). (cmb)

- Win32:
  . Fixed bug #76459 (windows linkinfo lacks openbasedir check). (Anatol)

- ZIP:
  . Fixed bug #76461 (OPSYS_Z_CPM defined instead of OPSYS_CPM).
    (Dennis Birkholz, Remi)

07 Jun 2018, PHP 7.2.7

- Core:
  . Fixed bug #76337 (segfault when opcache enabled + extension use
    zend_register_class_alias). (xKhorasan)

- CLI Server:
  . Fixed bug #76333 (PHP built-in server does not find files if root path
    contains special characters). (Anatol)

- OpenSSL:
  . Fixed bug #76296 (openssl_pkey_get_public does not respect open_basedir).
    (Erik Lax, Jakub Zelenka)
  . Fixed bug #76174 (openssl extension fails to build with LibreSSL 2.7).
    (Jakub Zelenka)

- SPL:
  . Fixed bug #76367 (NoRewindIterator segfault 11). (Laruence)

- Standard:
  . Fixed bug #76410 (SIGV in zend_mm_alloc_small). (Laruence)
  . Fixed bug #76335 ("link(): Bad file descriptor" with non-ASCII path).
    (Anatol)

24 May 2018, PHP 7.2.6

- EXIF:
  . Fixed bug #76164 (exif_read_data zend_mm_heap corrupted). (cmb)

- FPM:
  . Fixed bug #76075 --with-fpm-acl wrongly tries to find libacl on FreeBSD.
    (mgorny)

- intl:
  . Fixed bug #74385 (Locale::parseLocale() broken with some arguments).
    (Anatol)

- Opcache:
  . Fixed bug #76205 (PHP-FPM sporadic crash when running Infinitewp). (Dmitry)
  . Fixed bug #76275 (Assertion failure in file cache when unserializing empty
    try_catch_array). (Nikita)
  . Fixed bug #76281 (Opcache causes incorrect "undefined variable" errors).
    (Nikita)

- Reflection:
  . Fixed arginfo of array_replace(_recursive) and array_merge(_recursive).
    (carusogabriel)

- Session:
  . Fixed bug #74892 (Url Rewriting (trans_sid) not working on urls that start
    with "#"). (Andrew Nester)

26 Apr 2018, PHP 7.2.5

- Core:
  . Fixed bug #75722 (Convert valgrind detection to configure option).
    (Michael Heimpold)

- Date:
  . Fixed bug #76131 (mismatch arginfo for date_create). (carusogabriel)

- Exif:
  . Fixed bug #76130 (Heap Buffer Overflow (READ: 1786) in exif_iif_add_value).
    (Stas)

- FPM:
  . Fixed bug #68440 (ERROR: failed to reload: execvp() failed: Argument list
    too long). (Jacob Hipps)
  . Fixed incorrect write to getenv result in FPM reload. (Jakub Zelenka)

- GD:
  . Fixed bug #52070 (imagedashedline() - dashed line sometimes is not visible).
    (cmb)

- iconv:
  . Fixed bug #76249 (stream filter convert.iconv leads to infinite loop on
    invalid sequence). (Stas)

- intl:
  . Fixed bug #76153 (Intl compilation fails with icu4c 61.1). (Anatol)

- ldap:
  . Fixed bug #76248 (Malicious LDAP-Server Response causes Crash). (Stas)

- mbstring:
  . Fixed bug #75944 (Wrong cp1251 detection). (dmk001)
  . Fixed bug #76113 (mbstring does not build with Oniguruma 6.8.1).
    (chrullrich, cmb)

- ODBC:
  . Fixed bug #76088 (ODBC functions are not available by default on Windows).
    (cmb)

- Opcache:
  . Fixed bug #76094 (Access violation when using opcache). (Laruence)

- Phar:
  . Fixed bug #76129 (fix for CVE-2018-5712 may not be complete). (Stas)

- phpdbg:
  . Fixed bug #76143 (Memory corruption: arbitrary NUL overwrite). (Laruence)

- SPL:
  . Fixed bug #76131 (mismatch arginfo for splarray constructor).
    (carusogabriel)

- standard:
  . Fixed bug #74139 (mail.add_x_header default inconsistent with docs). (cmb)
  . Fixed bug #75996 (incorrect url in header for mt_rand). (tatarbj)

29 Mar 2018, PHP 7.2.4

- Core:
  . Fixed bug #76025 (Segfault while throwing exception in error_handler).
    (Dmitry, Laruence)
  . Fixed bug #76044 ('date: illegal option -- -' in ./configure on FreeBSD).
    (Anatol)

- FPM:
  . Fixed bug #75605 (Dumpable FPM child processes allow bypassing opcache
    access controls). (Jakub Zelenka)

- FTP:
  . Fixed ftp_pasv arginfo. (carusogabriel)

-GD:
  . Fixed bug #73957 (signed integer conversion in imagescale()). (cmb)
  . Fixed bug #76041 (null pointer access crashed php). (cmb)
  . Fixed imagesetinterpolation arginfo. (Gabriel Caruso)

- iconv:
  . Fixed bug #75867 (Freeing uninitialized pointer). (Philip Prindeville)

- Mbstring:
  . Fixed bug #62545 (wrong unicode mapping in some charsets). (cmb)

- Opcache:
  . Fixed bug #75969 (Assertion failure in live range DCE due to block pass
    misoptimization). (Nikita)

- OpenSSL:
  . Fixed openssl_* arginfos. (carusogabriel)

- PCNTL:
  . Fixed bug #75873 (pcntl_wexitstatus returns incorrect on Big_Endian platform
    (s390x)). (Sam Ding)

- Phar:
  . Fixed bug #76085 (Segmentation fault in buildFromIterator when directory
    name contains a \n). (Laruence)

- Standard:
  . Fixed bug #75961 (Strange references behavior). (Laruence)
  . Fixed some arginfos. (carusogabriel)
  . Fixed bug #76068 (parse_ini_string fails to parse "[foo]\nbar=1|>baz" with
    segfault). (Anatol)

01 Mar 2018, PHP 7.2.3

- Core:
  . Fixed bug #75864 ("stream_isatty" returns wrong value on s390x). (Sam Ding)

- Apache2Handler:
  . Fixed bug #75882 (a simple way for segfaults in threadsafe php just with
    configuration). (Anatol)

- Date:
  . Fixed bug #75857 (Timezone gets truncated when formatted). (carusogabriel)
  . Fixed bug #75928 (Argument 2 for `DateTimeZone::listIdentifiers()` should
    accept `null`). (Pedro Lacerda)
  . Fixed bug #68406 (calling var_dump on a DateTimeZone object modifies it).
    (jhdxr)

- LDAP:
  . Fixed bug #49876 (Fix LDAP path lookup on 64-bit distros). (dzuelke)

- libxml2:
  . Fixed bug #75871 (use pkg-config where available). (pmmaga)

- PGSQL:
  . Fixed bug #75838 (Memory leak in pg_escape_bytea()). (ard_1 at mail dot ru)

- Phar:
  . Fixed bug #54289 (Phar::extractTo() does not accept specific directories to
    be extracted). (bishop)
  . Fixed bug #65414 (deal with leading slash while adding files correctly).
    (bishopb)
  . Fixed bug #65414 (deal with leading slash when adding files correctly).
    (bishopb)

- ODBC:
  . Fixed bug #73725 (Unable to retrieve value of varchar(max) type). (Anatol)

- Opcache:
  . Fixed bug #75729 (opcache segfault when installing Bitrix). (Nikita)
  . Fixed bug #75893 (file_get_contents $http_response_header variable bugged
    with opcache). (Nikita)
  . Fixed bug #75938 (Modulus value not stored in variable). (Nikita)

- SPL:
  . Fixed bug #74519 (strange behavior of AppendIterator). (jhdxr)

- Standard:
  . Fixed bug #75916 (DNS_CAA record results contain garbage). (Mike,
    Philip Sharp)
  . Fixed bug #75981 (Prevent reading beyond buffer start in http wrapper).
    (Stas)

01 Feb 2018, PHP 7.2.2

- Core:
  . Fixed bug #75742 (potential memleak in internal classes's static members).
    (Laruence)
  . Fixed bug #75679 (Path 260 character problem). (Anatol)
  . Fixed bug #75614 (Some non-portable == in shell scripts). (jdolecek)
  . Fixed bug #75786 (segfault when using spread operator on generator passed
    by reference). (Nikita)
  . Fixed bug #75799 (arg of get_defined_functions is optional). (carusogabriel)
  . Fixed bug #75396 (Exit inside generator finally results in fatal error).
    (Nikita)

- FCGI:
  . Fixed bug #75794 (getenv() crashes on Windows 7.2.1 when second parameter is
    false). (Anatol)

- IMAP:
  . Fixed bug #75774 (imap_append HeapCorruction). (Anatol)

- Opcache:
  . Fixed bug #75720 (File cache not populated after SHM runs full). (Dmitry)
  . Fixed bug #75687 (var 8 (TMP) has array key type but not value type).
    (Nikita, Laruence)
  . Fixed bug #75698 (Using @ crashes php7.2-fpm). (Nikita)
  . Fixed bug #75579 (Interned strings buffer overflow may cause crash).
    (Dmitry)

- PDO:
  . Fixed bug #75616 (PDO extension doesn't allow to be built shared on Darwin).
    (jdolecek)

- PDO MySQL:
  . Fixed bug #75615 (PDO Mysql module can't be built as module). (jdolecek)

- PGSQL:
  . Fixed bug #75671 (pg_version() crashes when called on a connection to
    cockroach). (magicaltux at gmail dot com)

- Readline:
  . Fixed bug #75775 (readline_read_history segfaults with empty file).
    (Anatol)

- SAPI:
  . Fixed bug #75735 ([embed SAPI] Segmentation fault in
    sapi_register_post_entry). (Laruence)

- SOAP:
  . Fixed bug #70469 (SoapClient generates E_ERROR even if exceptions=1 is
    used). (Anton Artamonov)
  . Fixed bug #75502 (Segmentation fault in zend_string_release). (Nikita)

- SPL:
  . Fixed bug #75717 (RecursiveArrayIterator does not traverse arrays by
    reference). (Nikita)
  . Fixed bug #75242 (RecursiveArrayIterator doesn't have constants from parent
    class). (Nikita)
  . Fixed bug #73209 (RecursiveArrayIterator does not iterate object
    properties). (Nikita)

- Standard:
   . Fixed bug #75781 (substr_count incorrect result). (Laruence)
   . Fixed bug #75653 (array_values don't work on empty array). (Nikita)

- Zip:
  . Display headers (buildtime) and library (runtime) versions in phpinfo
    (with libzip >= 1.3.1). (Remi)

04 Jan 2018, PHP 7.2.1

- Core:
  . Fixed bug #75573 (Segmentation fault in 7.1.12 and 7.0.26). (Laruence)
  . Fixed bug #75384 (PHP seems incompatible with OneDrive files on demand).
    (Anatol)
  . Fixed bug #75525 (Access Violation in vcruntime140.dll). (Anatol)
  . Fixed bug #74862 (Unable to clone instance when private __clone defined).
    (Daniel Ciochiu)
  . Fixed bug #75074 (php-process crash when is_file() is used with strings
    longer 260 chars). (Anatol)
  . Fixed bug #69727 (Remove timestamps from build to make it reproducible).
    (jelle van der Waa)

- CLI server:
  . Fixed bug #73830 (Directory does not exist). (Anatol)

- FPM:
  . Fixed bug #64938 (libxml_disable_entity_loader setting is shared between
    requests). (Remi)

- GD:
  . Fixed bug #75571 (Potential infinite loop in gdImageCreateFromGifCtx).
    (Christoph)

- Opcache:
  . Fixed bug #75608 ("Narrowing occurred during type inference" error).
    (Laruence, Dmitry)
  . Fixed bug #75579 (Interned strings buffer overflow may cause crash).
    (Dmitry)
  . Fixed bug #75570 ("Narrowing occurred during type inference" error).
    (Dmitry)
  . Fixed bug #75681 (Warning: Narrowing occurred during type inference,
    specific case). (Nikita)
  . Fixed bug #75556 (Invalid opcode 138/1/1). (Laruence)

- PCRE:
  . Fixed bug #74183 (preg_last_error not returning error code after error).
    (Andrew Nester)

- Phar:
  . Fixed bug #74782 (remove file name from output to avoid XSS). (stas)

- Standard:
  . Fixed bug #75511 (fread not free unused buffer). (Laruence)
  . Fixed bug #75514 (mt_rand returns value outside [$min,$max]+ on 32-bit)
    (Remi)
  . Fixed bug #75535 (Inappropriately parsing HTTP response leads to PHP
    segment fault). (Nikita)
  . Fixed bug #75409 (accept EFAULT in addition to ENOSYS as indicator
    that getrandom() is missing). (sarciszewski)
  . Fixed bug #73124 (php_ini_scanned_files() not reporting correctly).
    (John Stevenson)
  . Fixed bug #75574 (putenv does not work properly if parameter contains
    non-ASCII unicode character). (Anatol)

- Zip:
  . Fixed bug #75540 (Segfault with libzip 1.3.1). (Remi)

30 Nov 2017, PHP 7.2.0

- BCMath:
  . Fixed bug #46564 (bcmod truncates fractionals). (liborm85)

- CLI:
  . Fixed bug #74849 (Process is started as interactive shell in PhpStorm).
    (Anatol)
  . Fixed bug #74979 (Interactive shell opening instead of script execution
    with -f flag). (Anatol)

- CLI server:
  . Fixed bug #60471 (Random "Invalid request (unexpected EOF)" using a router
    script). (SammyK)

- Core:
  . Added ZEND_COUNT, ZEND_GET_CLASS, ZEND_GET_CALLED_CLASS, ZEND_GET_TYPE,
    ZEND_FUNC_NUM_ARGS, ZEND_FUNC_GET_ARGS instructions, to implement
    corresponding builtin functions. (Dmitry)
  . "Countable" interface is moved from SPL to Core. (Dmitry)
  . Added ZEND_IN_ARRAY instruction, implementing optimized in_array() builtin
    function, through hash lookup in flipped array. (Dmitry)
  . Removed IS_TYPE_IMMUTABLE (it's the same as COPYABLE & !REFCOUNTED). (Dmitry)
  . Removed the sql.safe_mode directive. (Kalle)
  . Removed support for Netware. (Kalle)
  . Renamed ReflectionClass::isIterateable() to ReflectionClass::isIterable()
    (alias original name for BC). (Sara)
  . Fixed bug #54535 (WSA cleanup executes before MSHUTDOWN). (Kalle)
  . Implemented FR #69791 (Disallow mail header injections by extra headers)
    (Yasuo)
  . Implemented FR #49806 (proc_nice() for Windows). (Kalle)
  . Fix pthreads detection when cross-compiling (ffontaine)
  . Fixed memory leaks caused by exceptions thrown from destructors. (Bob,
    Dmitry).
  . Fixed bug #73215 (uniqid() should use better random source). (Yasuo)
  . Implemented FR #72768 (Add ENABLE_VIRTUAL_TERMINAL_PROCESSING flag for
    php.exe). (Michele Locati)
  . Implemented "Convert numeric keys in object/array casts" RFC, fixes
    bugs #53838, #61655, #66173, #70925, #72254, etc. (Andrea)
  . Implemented "Deprecate and Remove Bareword (Unquoted) Strings" RFC.
    (Rowan Collins)
  . Raised minimum supported Windows versions to Windows 7/Server 2008 R2.
    (Anatol)
  . Implemented minor optimization in array_keys/array_values(). (Sara)
  . Added PHP_OS_FAMILY constant to determine on which OS we are. (Jan Altensen)
  . Fixed bug #73987 (Method compatibility check looks to original
    definition and not parent). (pmmaga)
  . Fixed bug #73991 (JSON_OBJECT_AS_ARRAY not respected). (Sara)
  . Fixed bug #74053 (Corrupted class entries on shutdown when a destructor
    spawns another object). (jim at commercebyte dot com)
  . Fixed bug #73971 (Filename got limited to MAX_PATH on Win32 when scan
    directory). (Anatol)
  . Fixed bug #72359, bug #72451, bug #73706, bug #71115 and others related
    to interned strings handling in TS builds. (Anatol, Dmitry)
  . Implemented "Trailing Commas In List Syntax" RFC for group use lists only.
    (Sammy Kaye Powers)
  . Fixed bug #74269 (It's possible to override trait property with different
    loosely-equal value). (pmmaga)
  . Fixed bug #61970 (Restraining __construct() access level in subclass gives
    a fatal error). (pmmaga)
  . Fixed bug #63384 (Cannot override an abstract method with an abstract
    method). (pmmaga, wes)
  . Fixed bug #74607 (Traits enforce different inheritance rules). (pmmaga)
  . Fixed misparsing of abstract unix domain socket names. (Sara)
  . Change PHP_OS_FAMILY value from "OSX" to "Darwin". (Sebastian, Kalle)
  . Allow loading PHP/Zend extensions by name in ini files (extension=<name>).
    (francois at tekwire dot net)
  . Added object type annotation. (brzuchal)
  . Fixed bug #74815 (crash with a combination of INI entries at startup).
    (Anatol)
  . Fixed bug #74836 (isset on zero-prefixed numeric indexes in array broken).
    (Dmitry)
  . Added new VM instuctions ISSET_ISEMPTY_CV and UNSET_CV. Previously they
    were implemented as ISSET_ISEMPTY_VAR and UNSET_VAR variants with
    ZEND_QUICK_SET flag. (Nikita, Dmitry)
  . Fixed bug #49649 (unserialize() doesn't handle changes in property
    visibility). (pmmaga)
  . Fixed #74866 (extension_dir = "./ext" now use current directory for base).
    (Francois Laupretre)
  . Implemented FR #74963 (Improved error message on fetching property of
    non-object). (Laruence)
  . Fixed Bug #75142 (buildcheck.sh check for autoconf version needs to be updated
    for v2.64). (zizzy at zizzy dot net, Remi)
  . Fixed bug #74878 (Data race in ZTS builds). (Nikita, Dmitry)
  . Fixed bug #75515 ("stream_copy_to_stream" doesn't stream anymore). (Sara)

- cURL:
  . Fixed bug #75093 (OpenSSL support not detected). (Remi)
  . Better fix for #74125 (use pkg-config instead of curl-config). (Remi)

- Date:
  . Fixed bug #55407 (Impossible to prototype DateTime::createFromFormat).
    (kelunik)
  . Implemented FR #71520 (Adding the DateTime constants to the
    DateTimeInterface interface). (Majkl578)
  . Fixed bug #75149 (redefinition of typedefs ttinfo and t1info). (Remi)
  . Fixed bug #75222 (DateInterval microseconds property always 0). (jhdxr)

- Dba:
  . Fixed bug #72885 (flatfile: dba_fetch() fails to read replaced entry).
    (Anatol)

- DOM:
  . Implement #74837 (Implement Countable for DomNodeList and DOMNamedNodeMap).
    (Andreas Treichel)

- EXIF:
  . Added support for vendor specific tags for the following formats:
    Samsung, DJI, Panasonic, Sony, Pentax, Minolta, Sigma/Foveon, AGFA,
	Kyocera, Ricoh & Epson. (Kalle)
  . Fixed bug #72682 (exif_read_data() fails to read all data for some
    images). (Kalle)
  . Fixed bug #71534 (Type confusion in exif_read_data() leading to heap
    overflow in debug mode). (hlt99 at blinkenshell dot org, Kalle)
  . Fixed bug #68547 (Exif Header component value check error).
    (sjh21a at gmail dot com, Kalle)
  . Fixed bug #66443 (Corrupt EXIF header: maximum directory nesting level
    reached for some cameras). (Kalle)
  . Fixed Redhat bug #1362571 (PHP not returning full results for
    exif_read_data function). (Kalle)
  . Implemented #65187 (exif_read_data/thumbnail: add support for stream
    resource). (Kalle)
  . Deprecated the read_exif_data() alias. (Kalle)
  . Fixed bug #74428 (exif_read_data(): "Illegal IFD size" warning occurs with
    correct exif format). (bradpiccho at gmail dot com, Kalle)
  . Fixed bug #72819 (EXIF thumbnails not read anymore). (Kalle)
  . Fixed bug #62523 (php crashes with segfault when exif_read_data called).
    (Kalle)
  . Fixed bug #50660 (exif_read_data(): Illegal IFD offset (works fine with
    other exif readers). (skinny dot bravo at gmail dot com, Kalle)

- Fileinfo:
  . Upgrade bundled libmagic to 5.31. (Anatol)

- FPM:
  . Configuration to limit fpm slow log trace callers. (Sannis)
  . Fixed bug #75212 (php_value acts like php_admin_value). (Remi)

- FTP:
  . Implement MLSD for structured listing of directories. (blar)
  . Added ftp_append() function. (blar)

- GD:
  . Implemented imageresolution as getter and setter (Christoph)
  . Fixed bug #74744 (gd.h: stdarg.h include missing for va_list use in
    gdErrorMethod). (rainer dot jung at kippdata dot de, cmb)
  . Fixed bug #75111 (Memory disclosure or DoS via crafted .bmp image). (cmb)

- GMP:
  . Fixed bug #70896 (gmp_fact() silently ignores non-integer input). (Sara)

- Hash:
  . Changed HashContext from resource to object. (Rouven Weßling, Sara)
  . Disallowed usage of non-cryptographic hash functions with HMAC and PBKDF2.
    (Andrey Andreev, Nikita)
  . Fixed Bug #75284 (sha3 is not supported on bigendian machine). (Remi)

- IMAP:
  . Fixed bug #72324 (imap_mailboxmsginfo() return wrong size).
    (ronaldpoon at udomain dot com dot hk, Kalle)

- Intl:
  . Fixed bug #63790 (test using Spoofchecker which may be unavailable). (Sara)
  . Fixed bug #75378 ([REGRESSION] IntlDateFormatter::parse() does not change
    $position argument). (Laruence)

- JSON:
  . Add JSON_INVALID_UTF8_IGNORE and JSON_INVALID_UTF8_SUBSTITUTE options for
    json_encode and json_decode to ignore or replace invalid UTF-8 byte
    sequences - it addresses request #65082. (Jakub Zelenka)
  . Fixed bug #75185 (Buffer overflow in json_decode() with
    JSON_INVALID_UTF8_IGNORE or JSON_INVALID). (Jakub Zelenka)
  . Fixed bug #68567 (JSON_PARTIAL_OUTPUT_ON_ERROR can result in JSON with null
    key). (Jakub Zelenka)

- LDAP:
  . Implemented FR #69445 (Support for LDAP EXOP operations)
  . Fixed support for LDAP_OPT_SERVER_CONTROLS and LDAP_OPT_CLIENT_CONTROLS in ldap_get_option
  . Fixed passing an empty array to ldap_set_option for client or server controls.

- Mbstring:
  . Implemented request #66024 (mb_chr() and mb_ord()). (Masakielastic, Yasuo)
  . Implemented request #65081 (mb_scrub()). (Masakielastic, Yasuo)
  . Implemented request #69086 (enhancement for mb_convert_encoding() that
    handles multibyte replacement char nicely). (Masakielastic, Yasuo)
  . Added array input support to mb_convert_encoding(). (Yasuo)
  . Added array input support to mb_check_encoding(). (Yasuo)
  . Fixed bug #69079 (enhancement for mb_substitute_character). (masakielastic)
  . Update to oniguruma version 6.3.0. (Remi)
  . Fixed bug #69267 (mb_strtolower fails on titlecase characters). (Nikita)

- Mcrypt:
  . The deprecated mcrypt extension has been moved to PECL. (leigh)

- Opcache:
  . Added global optimisation passes based on data flow analysis using Single
    Static Assignment (SSA) form: Sparse Conditional Constant Propagation (SCCP),
    Dead Code Elimination (DCE), and removal of unused local variables
    (Nikita, Dmitry)
  . Fixed incorect constant conditional jump elimination. (Dmitry)
  . Fixed bug #75230 (Invalid opcode 49/1/8 using opcache). (Laruence)
  . Fixed bug (assertion fails with extended info generated). (Laruence)
  . Fixed bug (Phi sources removel). (Laruence)
  . Fixed bug #75370 (Webserver hangs on valid PHP text). (Laruence)
  . Fixed bug #75357 (segfault loading WordPress wp-admin). (Laruence)

- OpenSSL:
  . Use TLS_ANY for default ssl:// and tls:// negotiation. (kelunik)
  . Fix leak in openssl_spki_new(). (jelle at vdwaa dot nl)
  . Added openssl_pkcs7_read() and pk7 parameter to openssl_pkcs7_verify().
    (jelle at vdwaa dot nl)
  . Add ssl security_level stream option to support OpenSSL security levels.
    (Jakub Zelenka).
  . Allow setting SNI cert and private key in separate files. (Jakub Zelenka)
  . Fixed bug #74903 (openssl_pkcs7_encrypt() uses different EOL than before).
    (Anatol)
  . Automatically load OpenSSL configuration file. (Jakub Zelenka)

- PCRE:
  . Added support for PCRE JIT fast path API. (dmitry)
  . Fixed bug #61780 (Inconsistent PCRE captures in match results). (cmb)
  . Fixed bug #74873 (Minor BC break: PCRE_JIT changes output of preg_match()).
    (Dmitry)
  . Fixed bug #75089 (preg_grep() is not reporting PREG_BAD_UTF8_ERROR after
    first input string). (Dmitry)
  . Fixed bug #75223 (PCRE JIT broken in 7.2). (Dmitry)
  . Fixed bug #75285 (Broken build when system libpcre don't have jit support).
    (Remi)

- phar:
  . Fixed bug #74196 (phar does not correctly handle names containing dots).
    (mhagstrand)

- PDO:
  . Add "Sent SQL" to debug dump for emulated prepares. (Adam Baratz)
  . Add parameter types for national character set strings. (Adam Baratz)

- PDO_DBlib:
  . Fixed bug #73234 (Emulated statements let value dictate parameter type).
    (Adam Baratz)
  . Fixed bug #73396 (bigint columns are returned as strings). (Adam Baratz)
  . Expose DB-Library version as \PDO::DBLIB_ATTR_VERSION attribute on \PDO
    instance. (Adam Baratz)
  . Add test coverage for bug #72969. (Jeff Farr)

- PDO_OCI:
  . Fixed Bug #74537 (Align --with-pdo-oci configure option with --with-oci8 syntax).
    (Tianfang Yang)

- PDO_Sqlite
  . Switch to sqlite3_prepare_v2() and sqlite3_close_v2() functions (rasmus)

- PHPDBG
  . Added extended_value to opcode dump output. (Sara)

- Session:
  . Fixed bug #73461 (Prohibit session save handler recursion). (Yasuo)
  . PR #2233 Removed register_globals related code and "!" can be used as $_SESSION key name. (Yasuo)
  . Improved bug #73100 fix. 'user' save handler can only be set by session_set_save_handler()
  . Fixed bug #74514 (5 session functions incorrectly warn when calling in
    read-only/getter mode). (Yasuo)
  . Fixed bug #74936 (session_cache_expire/cache_limiter/save_path() trigger a
    warning in read mode). (morozov)
  . Fixed bug #74941 (session fails to start after having headers sent).
    (morozov)

- Sodium:
  . New cryptographic extension
  . Added missing bindings for libsodium > 1.0.13. (Frank)

- SPL:
  . Fixed bug #71412 (Incorrect arginfo for ArrayIterator::__construct).
    (tysonandre775 at hotmail dot com)
  . Added spl_object_id(). (Tyson Andre)

- SQLite3:
  . Implement writing to blobs. (bohwaz at github dot com)
  . Update to Sqlite 3.20.1. (cmb)

- Standard:
  . Fixed bug #69442 (closing of fd incorrect when PTS enabled). (jaytaph)
  . Fixed bug #74300 (unserialize accepts two plus/minus signs for float number exponent part).
    (xKerman)
  . Compatibility with libargon2 versions 20161029 and 20160821.
    (charlesportwoodii at erianna dot com)
  . Fixed Bug #74737 (mysqli_get_client_info reflection info).
    (mhagstrand at gmail dot com)
  . Add support for extension name as argument to dl().
    (francois at tekwire dot net)
  . Fixed bug #74851 (uniqid() without more_entropy performs badly).
    (Emmanuel Dreyfus)
  . Fixed bug #74103 (heap-use-after-free when unserializing invalid array
    size). (Nikita)
  . Fixed bug #75054 (A Denial of Service Vulnerability was found when
    performing deserialization). (Nikita)
  . Fixed bug #75170 (mt_rand() bias on 64-bit machines). (Nikita)
  . Fixed bug #75221 (Argon2i always throws NUL at the end). (cmb)

- Streams:
  . Default ssl/single_dh_use and ssl/honor_cipher_order to true. (kelunik)

- XML:
  . Moved utf8_encode() and utf8_decode() to the Standard extension. (Andrea)

- XMLRPC:
  . Use Zend MM for allocation in bundled libxmlrpc (Joe)

- ZIP:
  . Add support for encrypted archives. (Remi)
  . Use of bundled libzip is deprecated, --with-libzip option is recommended. (Remi)
  . Fixed Bug #73803 (Reflection of ZipArchive does not show public properties). (Remi)
  . ZipArchive implements countable, added ZipArchive::count() method. (Remi)
  . Fix segfault in php_stream_context_get_option call. (Remi)
  . Fixed bug #75143 (new method setEncryptionName() seems not to exist
    in ZipArchive). (Anatol)

- zlib:
  . Expose inflate_get_status() and inflate_get_read_len() functions.
    (Matthew Trescott)
