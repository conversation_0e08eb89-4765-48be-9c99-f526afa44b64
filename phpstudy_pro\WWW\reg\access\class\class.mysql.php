<?php
class MySQL {
    protected $conn;
    protected $res;

    /**
     * 初始化
     *
     * @param [array] $configArr $configArr['HOST'], $configArr['USER'], $configArr['PASS'], $configArr['BASE'], $configArr['PORT']
     */
    function __construct($configArr, $charset='utf8')  
    {

        $this -> __destruct();

        $this -> conn = mysqli_connect($configArr['HOST'], $configArr['USER'], $configArr['PASS'], $configArr['BASE'], $configArr['PORT']);
        
        $this ->query("SET NAMES '" . $charset . "';"); 
        return $this -> conn;

    }

    function __destruct()
    {
        $this -> close();

    }

    /**
     * 运行SQL语句
     *
     * @param string $sql
     * @return void
     */
    function query($sql = '')
    {

        if($this -> conn)
        {
            // var_dump($sql . '<br />');
            $this -> res = mysqli_query($this -> conn, $sql);

            return $this -> res;

        }

    }

    /**
     * 关闭mysql连接
     *
     * @return void
     */
    function close()
    {
        $this -> free();

        if($this -> conn) mysqli_close($this -> conn);
    }

    /**
     * 释放MySQL
     *
     * @return void
     */
    function free()
    {

        if(is_object($this -> res))
        {

            mysqli_free_result($this -> res);
            
            $this -> res = null;

        }

    }

    /**
     * 下一条记录
     *
     * @return void
     */
    function next()
    {

        if(is_object($this -> res))
        {

            return mysqli_fetch_assoc($this -> res);

        }
    }

    /**
     * 获取执行语句后受影响的行数
     *
     * @return void
     */
    function affected()
    {
        return mysqli_affected_rows($this->conn);
    }

    /**
     * 转义语句中的字符串为mysql的
     *
     * @param [type] $str
     * @return void
     */
    function replace($str = '')
    {
        if($this->conn){
            $str = mysqli_real_escape_string($this->conn, $str);
        }
        return $str;
    }

    /**
     * 获取最近一次mysql的错误信息
     *
     * @return void
     */
    function error()
    {
        return mysqli_error($this->conn);
    }

    /**
     * 获取结果集数量
     *
     * @return void
     */
    function num()
    {
        return mysqli_num_rows($this -> res);
    }
}