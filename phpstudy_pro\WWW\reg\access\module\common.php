<?php

require_once dirname(__FILE__) . "/../inc/config.php";
require_once dirname(__FILE__) . "/../class/discuz.authcode.php";
require_once dirname(__FILE__) . "/../class/class.mysql.php";

/**

 */


 function die_json($code, $msg = false, $data = null)
 {

    $arr = [
        "return_code" => $code
    ];

    if($msg)
    {

        $arr['return_msg'] = $msg;

    }

    if($data)
    {

        $arr['data'] = $data;

    }

    header("Server-Type:rxjh");

    header("Author:QQ:778716166");

    header("Content-Type: application/json;charset=UTF-8");

    die(json_encode($arr));

 }

   /**
   * 结束脚本以XML格式
   *
   * @param string $code
   * @param string $msg
   * @param [type] $data
   * @return void
   */
  function die_xml($code = "Fail", $msg = false, $data = null){
    $arr = [
        "return_code" => $code
    ];

    if($msg){
        
        $arr['return_msg'] = $msg;

    }

    if($data){
        
        $arr['data'] = $data;

    }

    header("Server-Type:rxjh");

    header("Author:QQ:349445551");

    header("Content-Type:application/xml;charset=UTF-8");

    die(arr2xml($arr));

}
function arr2xml($arr, $root = 'xml')
{

    $xml = '';

    foreach($arr as $key => $val){

        if(is_array($val)){

            $xml .= str_repeat("\t", 1) . arr2xml_ex($val, 1);

        }else{

            $xml .= str_repeat("\t", 1) . "<" . $key . ">" . xml_replace($val) . "</" . $key . ">" . PHP_EOL;

        }
        
    }

    $xml = '<?xml version="1.0" encoding="utf-8"?>' . PHP_EOL . '<' . $root . '>' . PHP_EOL . $xml . '</' . $root . '>';

    return $xml;

}

function arr2xml_ex($arr, $lev=0)
{

    $xml = '';

    foreach($arr as $key => $val){

        if(is_array($val)){

            $xml .= str_repeat("\t", $lev) . "<" . $key . ">" . PHP_EOL . arr2xml_ex($val, $lev + 1) . str_repeat("\t", $lev) . "</" . $key . ">" . PHP_EOL;

        }else{

            $xml .= str_repeat("\t", $lev) . "<" . $key . ">" . xml_replace($val) . "</" . $key . ">" . PHP_EOL;

        }
        
    }

    return $xml;

}

function xml_replace($val)
{

    $val = str_ireplace("<", "&lt;", $val);

    $val = str_ireplace(">", "&gt;", $val);

    $val = str_ireplace('"', "&quto;", $val);

    return $val;

}


function die_error($error_str)
{

    $html = '<div class="error-div">' . $error_str . '</div>';

    die($html);

}

/**
 * 是否是AJAx提交的
 * @return bool
 */
function isAjax(){

    if(isset($_SERVER['HTTP_X_REQUESTED_WITH']) && strtolower($_SERVER['HTTP_X_REQUESTED_WITH']) == 'xmlhttprequest'){

      return true;

    }else{

      return false;

    }
 }


/**
 * 是否是GET提交的
 */
 function isGet()
 {
    return $_SERVER['REQUEST_METHOD'] == 'GET' ? true : false;
 }


 /**
  * 是否为POST请求
  */
 function isPost()
 {
    return ($_SERVER['REQUEST_METHOD'] == 'POST' && (empty($_SERVER['HTTP_REFERER']) || preg_replace("~https?:\/\/([^\:\/]+).*~i", "\\1", $_SERVER['HTTP_REFERER']) == preg_replace("~([^\:]+).*~", "\\1", $_SERVER['HTTP_HOST']))) ? 1 : 0;
 }

/**
 * 数组加密
 *
 * @param [type] $arr 要加密的数组
 * @return string 返回加密后的字符串
 */
function arr_encry($arr)
{
    return authcode(base64_encode(json_encode($arr)),"ENCODE", WEB_SIGN_KEY);
}

/**
 * 数组解密
 *
 * @param [type] $str 要解密的字符串
 * @return array 返回解密后的数组
 */
function arr_decry($str)
{
    return json_decode(base64_decode(authcode($str,"DECODE", WEB_SIGN_KEY)), true);
}

/**
 * 获取当前脚本名称
 */
function php_self()
{
    $php_Self = substr($_SERVER['PHP_SELF'],strripos($_SERVER['PHP_SELF'],"/")+1);

    return $php_Self;
}

/**
 * 获取真实IP地址
 *
 * @return void
 */
function get_real_ip(){
    static $realip = NULL;

    if ($realip !== NULL) {

        return $realip;

    }

    if (isset($_SERVER)) {

        if (isset($_SERVER['HTTP_X_FORWARDED_FOR'])) {

            $arr = explode(',', $_SERVER['HTTP_X_FORWARDED_FOR']);

            /* 取X-Forwarded-For中第一个非unknown的有效IP字符串 */

            foreach ($arr AS $ip) {

                $ip = trim($ip);
     
                if ($ip != 'unknown') {

                    $realip = $ip;
     
                    break;

                }

            }

        } elseif (isset($_SERVER['HTTP_CLIENT_IP'])) {

            $realip = $_SERVER['HTTP_CLIENT_IP'];

        } else {

            if (isset($_SERVER['REMOTE_ADDR'])) {

                $realip = $_SERVER['REMOTE_ADDR'];

            } else {

                $realip = '0.0.0.0';

            }

        }

    } else {

        if (getenv('HTTP_X_FORWARDED_FOR')) {

            $realip = getenv('HTTP_X_FORWARDED_FOR');

        } elseif (getenv('HTTP_CLIENT_IP')) {

            $realip = getenv('HTTP_CLIENT_IP');

        } else {

            $realip = getenv('REMOTE_ADDR');

        }

    }

    // 使用正则验证IP地址的有效性，防止伪造IP地址进行SQL注入攻击

    preg_match("/[\d\.]{7,15}/", $realip, $onlineip);

    $realip = !empty($onlineip[0]) ? $onlineip[0] : '0.0.0.0';

    return $realip;

}

/**
 * 检测是否有特殊字符
 *
 * @param [type] $atr
 * @return boolean
 */
function isIllegalCode($str){

    if(stripos($str,"'") !== false) return true;

    if(stripos($str,'"') !== false) return true;

    if(stripos($str,'\\') !== false) return true;

}

/**
 * 判断是否为合法的身份证号码
 * @param $mobile
 * @return int
 */
function isCreditNo($vStr){
    $vCity = array(
     '11','12','13','14','15','21','22',
     '23','31','32','33','34','35','36',
     '37','41','42','43','44','45','46',
     '50','51','52','53','54','61','62',
     '63','64','65','71','81','82','91'
    );

    if (!preg_match('/^([\d]{17}[xX\d]|[\d]{15})$/', $vStr)) return false;

    if (!in_array(substr($vStr, 0, 2), $vCity)) return false;

    $vStr = preg_replace('/[xX]$/i', 'a', $vStr);

    $vLength = strlen($vStr);

    if ($vLength == 18) {

     $vBirthday = substr($vStr, 6, 4) . '-' . substr($vStr, 10, 2) . '-' . substr($vStr, 12, 2);

    } else {

     $vBirthday = '19' . substr($vStr, 6, 2) . '-' . substr($vStr, 8, 2) . '-' . substr($vStr, 10, 2);

    }

    if (date('Y-m-d', strtotime($vBirthday)) != $vBirthday) return false;

    if ($vLength == 18) {

     $vSum = 0;

     for ($i = 17 ; $i >= 0 ; $i--) {

      $vSubStr = substr($vStr, 17 - $i, 1);

      $vSum += (pow(2, $i) % 11) * (($vSubStr == 'a') ? 10 : intval($vSubStr , 11));

     }

     if($vSum % 11 != 1) return false;

    }

    return true;

}



/**
 * 获取签名，排除数组中参数名为sign和参数值为空的参数，注册所有值需要urlencode
 * 签名算法：
 * 将数组按照ASCII字典序进行ASC排序
 * 将数组非空参数按照url键值对形式将数组组合为字符串
 * 将签名密钥key=密钥 带入字符串末尾
 * 对字符串获取MD5并转换为大写得到签名字符串
 */

 function arr2sign($arr, $key = WEB_SIGN_KEY)
 {
    if(is_array($arr))
    {

        ksort($arr);
    
        $str = '';
    
        foreach($arr as $k => $v){
    
            if($v == null || $v == 'null')
            {
    
                $str .= (!$str ? '' : '&') . $k . "=" . urldecode($v);
    
            }
    
        }
    
        $str .= (!$str ? '' : '&') . 'key=' . $key;
    
        return strtoupper(md5($str));

    }

 }


 /**
  * 获取当前毫秒数
  *
  * @return float
  */
 function msectime()
 {

    list($msec, $sec) = explode(" ", microtime());

    $msectime = (float)sprintf('%.0f', (floatval($msec) + floatval($sec)) * 1000);

    return $msectime;

 }

 /**
 * 页码计算出数组
 *
 * @param integer $page
 * @param integer $all_page
 * @param integer $limit
 * @return void
 */
function ceilPageToArray($page = 1, $all_page = 1, $limit = 3){
    if($page < 1){
        $page = 1;
    }elseif($page > $all_page){
        $page = $all_page;
    }
    $arr = [];
    for($i = 1; $i <= $all_page; $i ++){
        $tmp = null;
        if($i == 1){
            // 第一页
            $tmp = [
                'code' => 'first',
                'page' => $i,
                'html' => $i
            ];
        }
        elseif($i == $all_page){
            // 最后一页
            $tmp = [
                'code' => 'last',
                'page' => $i,
                'html' => $i
            ];
        }

        elseif($i > $page - $limit && $i < $page){
            // 前N页
            $tmp = [
                'code' => 'before',
                'page' => $i,
                'html' => $i
            ];
        }

        
        elseif($i < $page + $limit && $i > $page){
            // 后N页
            $tmp = [
                'code' => 'after',
                'page' => $i,
                'html' => $i
            ];
        }

        elseif($i === $page - $limit){
            // 前N页之前的省略号
            $tmp = [
                'code' => 'ellipsis',
                'page' => '...',
                'html' => '...'
            ];
        }

        elseif($i === $page + $limit){
            // 后N页之后的省略号
            $tmp = [
                'code' => 'ellipsis',
                'page' => '...',
                'html' => '...'
            ];
        }
        
        if($i == $page){
            // 当前页
            $tmp = [
                'code' => 'current',
                'page' => $i,
                'html' => $i
            ];
        }
        


        if(is_array($tmp)){
            array_push($arr,$tmp);
        }
    }

    // 上下一页
    $tmp = [
        'code' => 'prev',
        'page' => ($page - 1 < 1 ? 1 : $page - 1),
        'html' => '上一页'
    ];
    array_unshift($arr,$tmp);
    $tmp = [
        'code' => 'next',
        'page' => ($page + 1 > $all_page ? $all_page : $page + 1),
        'html' => '下一页'
    ];
    array_push($arr,$tmp);

    return $arr;
}

/**
 * html转义保存在数据库（采用XML转义方式）
 *
 * @param [type] $html
 * @return string
 */
function html2desc($html){

    $html = str_ireplace("&", "&amp;", $html);  // 要放在开始，不然后面的就会被转换掉

    $html = str_ireplace("<", "&lt;", $html);

    $html = str_ireplace(">", "&gt;", $html);
    
    $html = str_ireplace("'", "&apos;", $html);

    $html = str_ireplace('"', "&quot;", $html);

    return $html;

}

/**
 * 字符串转义为HTML（采用XML转义方式）
 *
 * @param [type] $html
 * @return string
 */
function desc2html($str){

    $str = str_ireplace("&lt;", "<", $str);

    $str = str_ireplace("&gt;", ">", $str);
    
    $str = str_ireplace("&apos;", "'", $str);

    $str = str_ireplace("&quot;", '"', $str);

    $str = str_ireplace("&amp;", "&", $str);    // 要放在后面转换，不然之前的就会被替换掉

    return $str;

}

/**
 * POST访问网页
 *
 * @param [type] $data
 * @param [type] $url
 * @return any
 */
function post($data, $url) {

    $ch = curl_init();      // 初始化CURL

    curl_setopt($ch, CURLOPT_URL, $url);    // 设置URL

    curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);

    curl_setopt($ch, CURLOPT_POST, 1);  // 如果你想PHP去做一个正规的HTTP POST，设置这个选项为一个非零值。这个POST是普通的 application/x-www-from-urlencoded 类型，多数被HTML表单使用。

    curl_setopt($ch, CURLOPT_POSTFIELDS, $data);    // 传递一个作为HTTP "POST"操作的所有数据的字符串。

    curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);

    curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, 0);

    $rst = curl_exec($ch);

    curl_close($ch);

    return $rst;
}


function get_upload_max_filesize_byte($dec=2){

    $max_size=ini_get('upload_max_filesize');

    preg_match('/(^[0-9\.]+)(\w+)/',$max_size,$info);

    $size = $info[1];

    $suffix = strtoupper($info[2]);

    $a = array_flip(array("B", "KB", "MB", "GB", "TB", "PB"));

    $b = array_flip(array("B", "K", "M", "G", "T", "P"));

    $pos = $a[$suffix] && $a[$suffix] !==0 ? $a[$suffix] : $b[$suffix];

    return round($size*pow(1024,$pos),$dec);

}

/**正则压缩HTML页面数据 */
function compress_html($string) {

    return ltrim(rtrim(preg_replace(array("/> *([^ ]*) *</","//","'/\*[^*]*\*/'","/\r\n/","/\n/","/\t/",'/>[ ]+</'),
           array(">\\1<",'','','','','','><'),$string)));

}

// 移除HTML中的<!-- -->注释
function remove_html_comments($content = '') {

	// return preg_replace('/<!--(.|\s)*?-->/', '', $content);

    return  preg_replace("#<!--[^\!\[]*?(?<!\/\/)-->#", '', $content);

}


function remove_html_space($html){

    $arr = explode(PHP_EOL, $html);

    $temp = '';

    foreach($arr as $val)
    {

        $line = trim($val);
        if($val != ''){

            $temp .= $line;

        }
        

    }

    return $temp;

}

/**
 * 超强压缩（三种压缩方式），注意：必须保证HTML、JS、CSS等代码正确，否则将出现不可预料的错误
 *
 * @param [type] $html  // HTML网页代码（可包含CSS，JS，CSS）
 * @param boolean $remove_space // 是否移除空格
 * @param boolean $remove_comments  // 是否移除注释
 * @param boolean $compress // 是否压缩
 * @return void
 */
function compress_html_ex($html, $remove_space = true, $remove_comments = true, $compress = true){

    if($remove_space){
        
        $html = remove_html_space($html);   // 删除空行

    }
    
    if($remove_comments){

        $html = remove_html_comments($html);    // 移除所有注释

    }

    if($compress){

        $html = compress_html($html);   // 压缩html

    }

    return $html;

}

function html_preg_str($html){

    $str = preg_replace("~<([a-z]+?)\s+?.*?>~i", '', $html);

    return strip_tags($str);    // 使用php自带的去掉html标签

}

/**
    * [delDirAndFile 删除指定文件夹及文件]
    * @param  [type] $dirName      [需要删除的目录]
    * @return [type]               [无]
*/
function delDirAndFile( $dirName='' ) { 
    // opendir将目录打开，成功则返回句柄资源
    if ( $handle = opendir( "$dirName" ) ) { 
        // while循环取出资源中的文件夹名称及文件名
        while ( false !== ( $item = readdir( $handle ) ) ) { 
            // 过滤掉"."，".."
            if ( $item != "." && $item != ".." ) { 
                // 检查当次循环是否为文件夹
                if ( is_dir( "$dirName/$item" ) ) { 
                    // 递归继续打开下层目录直至文件
                    delDirAndFile( "$dirName/$item" ); 
                } else { 
                    // 删除文件
                    if( unlink( "$dirName/$item" ) );
                } 
            } 
        } 
        closedir( $handle ); // 释放句柄资源
        if(rmdir( $dirName ));
        
    } 
}

function pageArrToHtml($pageArr, $url = ''){
    $html = '';
    $css = '';
    for($i = 0; $i < count($pageArr); $i++){
        $css = '';
        switch($pageArr[$i]['code']){
            case 'first' :
                break;
            case 'last' :
                break;
            case 'before' :
                break;
            case 'after' :
                break;
            case 'current' :
                $css = 'current';
                break;
            case 'ellipsis' :
                $css = 'none';
                break;
            case 'prev' :
                $css = 'prev';
                break;
            case 'next' :
                $css = 'next';
                break;
        }
        $html .= '<a class="' . $css . '" href="' . ($url ? sprintf($url, ($pageArr[$i]['page'] == 1 ? '' : $pageArr[$i]['page'])) : 'javascript:;') . '">' . $pageArr[$i]['html'] . '</a>';
    }
    return $html;
}


function num2desc($num){
    if(is_numeric($num)){
        $str = '';
        if($num >= 100000000){
            $ceil = floor($num / 100000000);
            $num = ($num % 100000000);
            $str .= $ceil . '亿'; 
        }
        if($num >= 10000){
            $ceil = floor($num / 10000);
            $num = ($num % 10000);
            $str .= $ceil . '万';
        }
        if($num > 0){
            $str .= $num;
        }
        

        return $str;
    }

}

/**
 * 重写php的dechex的函数，因为PHP是小端低位，大端高位，但windows的是小端高位，大端低位，所以要翻转
 *
 * @param integer $num
 * @return string
 */
function dechex_ex(int $num){
    $str = bcdechex($num);
    $str = (strlen($str) != 8 ? str_repeat('0', 8 - strlen($str)) : '') . $str;
    $i   = 0;
    $tmp = '';
    while($i <= 8){
        $tmp .= substr($str, 8 - $i, 2);
        $i += 2;
    }
    return $tmp;
}

/**
 * 重写php的hexdec的函数，因为PHP是小端低位，大端高位，但windows的是小端高位，大端低位，所以要翻转
 *
 * @param string $str
 * @return integer
 */
function hexdec_ex(string $str){
    $len = 8 - strlen($str);
    if($len > 0){
        $str = (strlen($str) != 8 ? str_repeat('0', $len) : '') . $str;
    }
    
    $i   = 0;
    $tmp = '';
    while($i <= 8){
        $tmp .= substr($str, 8 - $i, 2);
        $i += 2;
    }
    return bchexdec($tmp);
}

function format_hex(string $hex, $slipt = ' '){
    $str = '';
    $i = 0;
    while($i <= strlen($str)){
        $str .= ($str ? $slipt : '') . substr($hex, $i, 2);
        $i += 2;
    }
    return $str;
}

function bchexdec($hex)
{
    $dec = 0;
    $len = strlen($hex);
    for ($i = 1; $i <= $len; $i++) {
        $dec = bcadd($dec, bcmul(strval(hexdec($hex[$i - 1])), bcpow('16', strval($len - $i))));
    }
    return $dec;
}

function bcdechex($dec) {

    $last = bcmod($dec,16);
    
    $remain = bcdiv(bcsub($dec,$last),16);
    
    if($remain == 0) {
    
        return dechex($last);
    
    }else{
    
        return bcdechex($remain).dechex($last);
    
    }
    
}

function is_https() {
    if ( !empty($_SERVER['HTTPS']) && strtolower($_SERVER['HTTPS']) !== 'off') {
        return true;
    } elseif ( isset($_SERVER['HTTP_X_FORWARDED_PROTO']) && $_SERVER['HTTP_X_FORWARDED_PROTO'] === 'https' ) {
        return true;
    } elseif ( !empty($_SERVER['HTTP_FRONT_END_HTTPS']) && strtolower($_SERVER['HTTP_FRONT_END_HTTPS']) !== 'off') {
        return true;
    }
    return false;
}

/**
 * 将字符串进行过滤转义
 *
 * @param [string] $str
 * @return string
 */
function str_filter($str){
    $str = str_ireplace("'", "&apos;", $str);
    //$str = str_ireplace("&", "&amp;", $str);
    $str = str_ireplace('"', "&quto;", $str);
    $str = str_ireplace("<", "&lt;", $str);
    $str = str_ireplace(">", "&gt;", $str);
    $str = str_ireplace("\\", "&u005c;", $str);
    return $str;
}

/**
 * 字符串转义还原
 *
 * @param [type] $str
 * @return void
 */
function str_filter_de($str){
    $str = str_ireplace("&apos;", "'", $str);
    $str = str_ireplace("&amp;", "&", $str);
    $str = str_ireplace("&quto;", '"', $str);
    $str = str_ireplace("&lt;", "<", $str);
    $str = str_ireplace("&gt;", ">", $str);
    $str = str_ireplace("&u005c;", "\\", $str);
    return $str;
}

/**
 * 验证图形验证码
 *
 * @param [string] $str
 * @return bool
 */
function vcode_verify($str){
    if(strtolower($str) != strtolower($_SESSION["CACHE_VCODE"])) return false;
    if(time() - $_SESSION['CACHE_TIMES'] > 60 * 60) return false; // 一小时过期
    unset($_SESSION['CACHE_TIMES']);
    unset($_SESSION["CACHE_VCODE"]);
    return true;
 }

 /**
  * 设置登录状态
  *
  * @param [type] $user_id
  * @param [type] $username
  * @return void
  */
 function setAdminLogin($user_id, $username){
     $_SESSION['CACHE_ADMIN_USER_ID']   = $user_id;
     $_SESSION['CACHE_ADMIN_USER_NAME'] = $username;
     return ($_SESSION['CACHE_ADMIN_USER_ID'] === $user_id);
 }
/**
 * 注销管理员登录
 *
 * @return void
 */
 function setAdminLogout(){
     unset($_SESSION['CACHE_ADMIN_USER_ID']);
     unset($_SESSION['CACHE_ADMIN_USER_NAME']);
 }
 /**
  * 管理员是否登录
  *
  * @return bool
  */
 function adminIsLogin(){
     return ($_SESSION['CACHE_ADMIN_USER_ID'] && $_SESSION['CACHE_ADMIN_USER_NAME']);
 }

 function adminGetCacheUserNo(){
     return (int)$_SESSION['CACHE_ADMIN_USER_ID'];
 }
 function adminGetCacheUsername(){
     return $_SESSION['CACHE_ADMIN_USER_NAME'];
 }


 /**
  * 生成获取网页的导航栏
  *
  * @return html
  */
 function generateWebNavigation(){
    $mysql = new MySQL($GLOBALS['WebMySQL']);
    $html = '';
    if($mysql){
        $mysql1 = new MySQL($GLOBALS['WebMySQL']);
        // 先提取父级导航
        $sql = "SELECT * FROM tb_navigation WHERE (cParentId = 0 OR cParentId IS NULL) AND cInVersion<>1 ORDER BY cParentId ASC, cPriority DESC;";
        if($mysql->query($sql)){
            while($row = $mysql->next()){

                $sql = "SELECT * FROM tb_navigation WHERE cParentId=" . (int)$row['cNo'] . ";";
                $child = '';
                if($mysql1->query($sql)){
                    while($arr = $mysql1->next()){
                        $child .= '<dd><a href="' . (!$arr ['cUrl'] ? 'javascript:;' : $arr ['cUrl']) . '"' . ($arr['cNewTab'] == 1 ? (!$arr['cUrl'] ? '' : ' target="_blank"') : '') . '>' . $arr ['cName'] . '</a></dd>';
                    }
                    $mysql1->free();    // 释放
                }
                $html .= '<dl><dt><a href="' . (!$row ['cUrl'] ? 'javascript:;' : $row ['cUrl']) . '"' . ($row['cNewTab'] == 1 ? (!$row['cUrl'] ? '' : ' target="_blank"') : '') . '><h3>' . $row ['cChildName'] . '</h3><h2>' . $row ['cName'] . '</h2></a></dt>' . $child . '</dl>';
            }
            $mysql->free();    // 释放
        }
    }
    return $html;
 }

 /**
  * 生成default页面顶部的一些文章
  *
  * @return void
  */
 function generateDefaultNavNews(){
    $mysql = new MySQL($GLOBALS['WebMySQL']);
    $html = '';
    if($mysql){
        $sql = "SELECT * FROM tb_article WHERE (cDelete = 0 OR cDelete IS NULL) AND cImageFlag <> '' AND cImageFlag IS NOT NULL AND cInNavigation=1 AND cHidden<>1 AND cInVersion<>1 ORDER BY cDateCreate DESC LIMIT 4;";
        if($mysql->query($sql)){
            while($row = $mysql->next()){
                $html .= '<a href="/news-' . $row['cNo'] . '.html" target="_blank"><div class="block"><img src="' . str_filter_de($row['cImageFlag']) . '" alt=""><p>' . str_filter_de($row['cTitle']) . '</p></div></a>';
            }
            $mysql->free();
        }
    }
    return $html;
 }
 /**
  * 生成default页面顶部的一些文章
  *
  * @return void
  */
 function generateDefaultUpdateHtml(){
    $mysql = new MySQL($GLOBALS['WebMySQL']);
    $html = '';
    if($mysql){
        $sql = "SELECT * FROM tb_article WHERE (cDelete = 0 OR cDelete IS NULL) AND cInNavigation=1 AND cInHomeSlider <>1 AND cHidden<>1 AND cInVersion<>1 ORDER BY cDateCreate DESC LIMIT 3;";
        if($mysql->query($sql)){
            while($row = $mysql->next()){
                $html .= '<div class="line"><a href="/news-' . $row['cNo'] . '.html" target="_blank"><span class="f-date">' . date('Y/m/d', strtotime($row['cDateCreate'])) . '</span> | ' . str_filter_de($row['cTitle']) . '</a></div>';
            }
            $mysql->free();
        }
    }
    return $html;
 }
 /**
  * 生成main页面顶部的一些文章
  *
  * @return void
  */
 function generateMainNavNews(){
    $mysql = new MySQL($GLOBALS['WebMySQL']);
    $html = '';
    if($mysql){
        $sql = "SELECT * FROM tb_article WHERE (cDelete = 0 OR cDelete IS NULL) AND cImageFlag <> '' AND cImageFlag IS NOT NULL AND cInNavigation=1 AND cInHomeSlider <>1 AND cHidden<>1 AND cInVersion<>1 ORDER BY cDateCreate DESC LIMIT 3;";
        if($mysql->query($sql)){
            while($row = $mysql->next()){
                $html .= '<div><a href="/news-' . $row['cNo'] . '.html" target="_blank"><img src="' . str_filter_de($row['cImageFlag']) . '" alt=""><p>' . str_filter_de($row['cTitle']) . '</p></a></div>';
            }
            $mysql->free();
        }
    }
    return $html;
 }
/**
  * 生成main页面顶部的一些文章
  *
  * @return void
  */
 function generateMainNews(){
    $mysql = new MySQL($GLOBALS['WebMySQL']);
    $html = '';
    if($mysql){
        $sql = "SELECT * FROM tb_article WHERE (cDelete = 0 OR cDelete IS NULL) AND cImageFlag <> '' AND cImageFlag IS NOT NULL AND cInHomeSlider <>1 AND cHidden<>1 AND cInVersion<>1 ORDER BY cDateCreate DESC LIMIT 3;";
        if($mysql->query($sql)){
            while($row = $mysql->next()){
                $html .= '<a href="/news-' . $row['cNo'] . '.html" target="_blank"><div class="item"><div class="item-img"><img src="' . str_filter_de($row['cImageFlag']) . '" alt=""></div><div class="item-info"><h2>' . str_filter_de($row['cTitle']) . '</h2><div>' . str_filter_de($row['cChildTitle']) . '</div><p><span class="date">' . date('Y-m-d', strtotime($row['cDateCreate'])) . '</span><span class="more"></span></p></div></div></a>';
            }
            $mysql->free();
        }
    }
    return $html;
 }
/**
  * 生成main页面顶部的一些文章
  *
  * @return void
  */
 function generateMainSlider(){
    $mysql = new MySQL($GLOBALS['WebMySQL']);
    $html = '';
    if($mysql){
        $sql = "SELECT * FROM tb_article WHERE (cDelete = 0 OR cDelete IS NULL) AND cImageFlag <> '' AND cImageFlag IS NOT NULL AND cInHomeSlider=1 AND cHidden<>1 AND cInVersion<>1 ORDER BY cDateCreate DESC LIMIT 3;";
        if($mysql->query($sql)){
            while($row = $mysql->next()){
                $html .= '<div class="item"><img src="' . str_filter_de($row['cImageFlag']) . '" alt=""><div class="info"><div class="part"><div><img src="" alt=""></div><div class="big-title">' . str_filter_de($row['cTitle']) . '</div><div class="explain">' . str_filter_de($row['cChildTitle']) . '</div><div class="more"><a href="/news-' . $row['cNo'] . '.html" target="_blank"></a></div></div></div></div>';
            }
            $mysql->free();
        }
    }
    return $html;
 }

 /**
  * 生成news页面顶部的一些文章
  *
  * @return void
  */
 function generateNewsList($page = 1){
    $mysql = new MySQL($GLOBALS['WebMySQL']);
    $html = '';
    $page = ($page < 1 ? 1 : $page);
    $limit = 10;
    if($mysql){
        $sql = "SELECT * FROM tb_article WHERE (cDelete = 0 OR cDelete IS NULL) AND cInHomeSlider<>1 AND cHidden<>1 AND cInVersion<>1 ORDER BY cDateCreate DESC LIMIT " . (($page - 1) * $limit) . "," . $limit . ";";
        if($mysql->query($sql)){
            while($row = $mysql->next()){
                $html .= '<a href="/news-' . $row['cNo'] . '.html" target="_blank"><div class="item"><div class="img"><div><img src="' . str_filter_de($row['cImageFlag']) . '" alt=""></div></div><div class="context"><h2>' . str_filter_de($row['cTitle']) . '</h2><div>' . str_filter_de($row['cChildTitle']) . '</div><p><span>' . date('Y-m-d', strtotime($row['cDateCreate'])) . '</span><span class="right"></span></p></div></div></a>';
            }
            $mysql->free();
        }
    }
    return $html;
 }
/**
  * 生成news页面顶部的一些文章
  *
  * @return void
  */
 function generateNewsHeadHtml(){
    $mysql = new MySQL($GLOBALS['WebMySQL']);
    $html = '';
    if($mysql){
        $sql = "SELECT * FROM tb_article WHERE (cDelete = 0 OR cDelete IS NULL) AND cImageFlag <> '' AND cImageFlag IS NOT NULL AND cInHomeSlider<>1 AND cInVersion<>1 AND cInListHeadArea=1 AND cHidden<>1 ORDER BY cDateCreate DESC LIMIT 6;";
        if($mysql->query($sql)){
            $count = 0;
            while($row = $mysql->next()){
                $count ++;
                if($count <= 2){
                    $html .= '<a href="/news-' . $row['cNo'] . '.html" target="_blank"><div class="big big' . $count . '"><img src="' . str_filter_de($row['cImageFlag']) . '" alt=""></div></a>';
                }else{
                    $html .= '<a href="/news-' . $row['cNo'] . '.html" target="_blank"><div class="small small' . ($count - 2) . '"><img src="' . str_filter_de($row['cImageFlag']) . '" alt=""></div></a>';
                }
            }
            $mysql->free();
        }
    }
    return $html;
 }


 /**
  * 获取版本更迭的网页
  *
  * @return void
  */
 function generateVersionUpdateRecordHtml(){
    $mysql = new MySQL($GLOBALS['WebMySQL']);
    $html = '';
    if($mysql){
        $mysql1 = new MySQL($GLOBALS['WebMySQL']);
        // 先获取导航
        $sql = "SELECT * FROM tb_navigation WHERE cInVersion=1 ORDER BY cPriority DESC;";
        if($mysql->query($sql)){
            while($row = $mysql->next()){
                $sql = "SELECT * FROM tb_article WHERE cChildTitle LIKE '" . $row['cNo'] . ".%' AND (cDelete = 0 OR cDelete IS NULL) AND cInVersion=1 ORDER BY cDateCreate ASC,cChildTitle ASC;";
                $child = '';
                if($mysql1->query($sql)){
                    $count = 0;
                    $ver_gd_tmp = 0;
                    $tmp_child = '';
                    while($arr = $mysql1->next()){
                        $txt = explode('.', $arr['cChildTitle']);
                        if(count($txt) >= 2){
                            $ver_id = (int)$txt[0];
                            $ver_gd = (int)$txt[1];
                        }else{
                            $ver_id = (int)$arr['cChildTitle'];
                            $ver_gd = 0;
                        }

                        if($ver_id == $row['cNo']){
                            if($ver_gd_tmp != $ver_gd){
                                // 子版本不同重置计数器
                                $ver_gd_tmp = $ver_gd;
                                $count = 0;
                            }
                            $count ++;
                            if($count == 1){
                                if($tmp_child){
                                    $child .= '<div>' . $tmp_child . ($row['cUrl'] ? '<div class="version ver-link"><a href="' . $row['cUrl'] . '" target="_blank">版本专题网站 &gt;</a></div>' : '') . '</div>';
                                }
                                $tmp_child = '<div class="version ver-img"><img src="' . str_filter_de($arr['cImageFlag']) . '" alt=""></div>';
                            }
                            $tmp_child .= '<div class="version"><a href="/news-' . $arr['cNo'] .'.html" target="_blank">' . str_filter_de($arr['cTitle']) . '</a></div>';
                        }
                        
                    }
                    if($tmp_child){
                        $child .= '<div>' . $tmp_child . ($row['cUrl'] ? '<div class="version ver-link"><a href="' . $row['cUrl'] . '" target="_blank">版本专题网站 &gt;</a></div>' : '') . '</div>';
                    }
                    $mysql1->free();
                }

                if(!$child){
                    $child = '<div style="cont-size:16px; text-align:center; padding: 50px 0;">暂无版本更迭笔记！</div>';
                }

                $html .= '<div class="ver ver-hide" data-id="' . $row['cNo'] . '">' . $child . '</div>';
            }
        }

    }
    return $html;
 }

 function generateVersionUpdateMenu(){
    $mysql = new MySQL($GLOBALS['WebMySQL']);
    $html = '';
    if($mysql){
        $sql = "SELECT * FROM tb_navigation WHERE cInVersion=1 ORDER BY cDateCreate DESC LIMIT 10;";
        if($mysql->query($sql)){
            while($row = $mysql->next()){
                $html .= '<div data-id="' . $row['cNo'] . '">' . $row['cName'] . '</div>';
            }
            $mysql->free();
        }
    }
    return $html;
 }