*{margin: 0; padding: 0; box-sizing: border-box; font-family: Arial, Helvetica, sans-serif, 'microsoft yahei';}
html{font-size: 14px; background-color: #17181E; color: #999797; overflow-x: hidden;}

a{font-size: 14px; display: inline-block; height: 100%; color: #999797; text-decoration: none;}
a:hover{color: #ffffff;}

.main-bg-video{width: 100%; height: 900px; min-width: 1200px; position: absolute; left: 0; top: 0; z-index: -1;}
.main-bg-video video{width: 100%; height: 100%; object-fit: cover; pointer-events: none;}
.warp{height: 80px; background-color: rgba(0,0,0,.75); z-index: 2; position: absolute; left: 0; top: 0; width: 100%; overflow: hidden; transition: all .3s;}
.warp-show{height: 350px;}
.warp hr{border: none; border-top: 1px solid #454545; position: absolute; left: 0; top: 80px; width: 100%;}
.warp .content{height: 100%; width: calc(100% - 100px); margin: auto; white-space: nowrap; min-width: 1200px; position: relative;}
.warp .content>div{display: inline-block; vertical-align: top; height: 80px;}
.warp .logo img{height: 100%;}
.warp .menu{float: right; height: 100%; white-space: nowrap;}
.warp .menu>div{font-size: 0; display: inline-block; vertical-align: top;}
.warp .menu dl{display: inline-block; width: 120px; vertical-align: top; text-align: center; position: relative; z-index: 1;}
.warp .menu dt{height: 80px; padding-top: 25px; margin-bottom: 20px; position: relative;}
.warp .menu dt::before{content: ''; height: 1px; width: 0; left: 50%; bottom: -1px; position: absolute; background-color: #4fbcf3; transition: all .3s;}
.warp .menu dd{height: 35px; line-height: 35px;  font-weight: bold;}
.warp .menu dd a{font-size: 16px;}
.warp .menu .current::before{width: 70px; left: calc(50% - 35px);}
.warp .menu h3{font-weight: normal; font-size: 10px; opacity: .5;}
.warp .menu h2{font-size: 16px;}
.warp .right-button{padding-left: 20px; padding-top: 25px;}
.warp .content .recomm{position: absolute; height: 280px; width: 100%; white-space: nowrap; left: 0; top: 80px; padding: 20px 0;}
.warp .content .recomm>div{display: inline-block; vertical-align: top; height: 100%; width: 350px; padding-right: 20px; padding-top: 20px; text-align: center;}
.warp .content .recomm img{width: 100%; height: 150px; border: 1px solid #000000;}
.warp .content .recomm a{padding: 0; width: 100%; font-weight: bold; font-size: 16px;}
.nav-theme-btn{padding: 0; margin-left: 30px; }
.nav-theme-btn>div{height: 30px; padding: 0 20px; border: 1px solid #367797; color: #49a3cf; line-height: 30px; position: relative; font-size: 16px; font-weight: bold;}
.nav-theme-btn div::before{content: ''; position: absolute; left: 0; top: 0; width: 3px; height: 3px; background-color: #49a3cf;}
.nav-theme-btn div::after{content: ''; position: absolute; right: 0; top: 0; width: 3px; height: 3px; background-color: #49a3cf;}
.nav-theme-btn div p::before{content: ''; position: absolute; left: 0; bottom: 0; width: 3px; height: 3px; background-color: #49a3cf;}
.nav-theme-btn div p::after{content: ''; position: absolute; right: 0; bottom: 0; width: 3px; height: 3px; background-color: #49a3cf;}
.nav-theme-btn:hover div{border: 1px solid transparent; background: linear-gradient(to right, #495ab3, #409cca, #e68c32); color: #ffffff; box-shadow: 0 0 20px 0 #495ab3;}
.nav-theme-btn:hover div::before{display: none;}
.nav-theme-btn:hover div::after{display: none;}
.nav-theme-btn:hover div p::before{display: none;}
.nav-theme-btn:hover div p::after{display: none;}

.home-slogin{height: 900px; width: 100%; text-align: center; padding-top: 380px;}
.home-slogin .nav{display: inline-block; width: 377px; height: 52px; background: url(../images/homedown.png) no-repeat; font-size: 0;}
.home-slogin .nav a{display: inline-block; text-indent: -99999px; width: calc(100% / 2); height: 100%;}
.home-slogin .version{display: inline-block; width: 305px; height: 32px; line-height: 32px; background: url(../images/patch0713.png) no-repeat; margin: 20px 0;}
.home-slogin .version a{font-weight: bold; color: #49a3cf; letter-spacing: 3px; padding: 0;}
.web{width: 1200px; margin: auto;}
.news-box{padding-top: 50px;}
.news-box .news-title{background: url(../images/homenew.png) no-repeat 50% 50%; height: 85px; text-align: center; font-size: 26px; font-weight: bold; letter-spacing: 5px; padding-top: 25px; color: #ffffff;}
.news-box .news-list{padding-top: 40px; white-space: nowrap;}
.news-box .item{display: inline-block; vertical-align: top; width: 375px; height: 320px; margin-right: 33px; background-color: #20222B;}
.news-box .item .item-img{height: 180px; overflow: hidden;}
.news-box .item .item-img img{width: 100%; height: 100%; transition: all .5s;}
.news-box .item:hover img{transform: scale(1.2);}
.news-box .item .item-info{padding: 18px 13px 13px 13px; color: #98A0B2; }
.news-box .item .item-info h2{font-weight: normal; font-size: 18px; color: #ffffff; word-break: break-all; white-space: normal; width: 100%; height: 45px;}
.news-box .item .item-info div{height: 50px; white-space: normal; overflow: hidden; width: 100%;}
.news-box .item .item-info p{color: #494d5c; font-size: 12px;}
.news-box .item .item-info>span{display: inline-block; vertical-align: middle;}
.news-box .item .item-info .more{width: 21px; height: 6px; margin-top: 4px; background: url(../images/hnewicon.png) no-repeat; float: right;}
.news-more{padding: 40px 0; text-align: center;}
.news-more a{display: inline-block; width: 183px; height: 47px; background: url(../images/common2.png) no-repeat; text-indent: -9999px;}
.news-more a:hover{background-position: 0 -47px;}
.home-slider{height: auto; white-space: nowrap; font-size: 0; width: 100%; overflow: hidden; position: relative; min-width: 1200px;}
.home-slider .box{position: relative; transition: all .3s; left: 0;}
.home-slider .item{height: 100%; display: inline-block; width: 100%; position: relative;}
.home-slider .item-show{z-index: 1;}
.home-slider .item img{width: 100%; height: 100%; left: 0; top: 0; object-fit: cover;}
.home-slider .item .info{position: absolute; left: 50%; width: 50%; top: 0; height: 100%; background-color: rgba(19,25,30,.9);}
.home-slider .item .part{width: 45%; position: absolute; left: 5%; top: 10%; padding-top: 11%;}
.home-slider .item .part img{width: auto; height: auto; position: absolute; left: 0; top: 0;}
.home-slider .item .part .big-title{font-size: 32px; position: relative; z-index: 1; font-weight: bold; color: #50bff7;}
.home-slider .item .part .explain{font-size: 18px; margin-top: 50px; letter-spacing: 5px; line-height: 40px;}
.home-slider .item .part .more{margin-top: 55px;}
.home-slider .item .part .more a{display: inline-block; width: 183px; height: 47px; background: url(../images/common2.png) no-repeat; background-position: -183px 0;}
.home-slider .item .part .more a:hover{background-position: -183px -47px;}
.home-slider>.lb{display: inline-block; width: 40px; height: 60px; position: absolute; z-index: 2; top: calc(50% - 30px); background: url(../svg/lb-right.svg) no-repeat 50% 50%; background-size: 100% auto;}
.home-slider>.prev{left: 2%; transform: rotate(180deg);}
.home-slider>.next{right: 2%;}
.home-bottom{padding: 30px 0 50px 0; background-color: #0F0F12; text-align: center; font-size: 12px; color: #57575A;}
.home-bottom a{font-size: 12px; color: #57575A;}
.home-bottom a:hover{color: #ffffff;}
.home-bottom .icon{padding: 20px 0; height: 130px; margin: auto; font-size: 0;}
.home-bottom .icon>div{display: inline-block; padding: 0 30px; height: 100%; vertical-align: top;}
.home-bottom .icon .big{display: inline-block; width: 48px; height: 48px; background-size: 100%; background-repeat: no-repeat; opacity: .3;}
.home-bottom .icon .weixin{background-image: url(../svg/icon-weixin.svg); }
.home-bottom .icon .weibo{background-image: url(../svg/icon-weibo.svg); }
.home-bottom .icon .bbs{background-image: url(../svg/icon-bbs.svg); }
.home-bottom .icon .name{font-size: 16px; font-weight: bold; margin-top: 10px;}
.home-bottom .icon .tb{display: table; height: 100%;  font-size: 12px;}
.home-bottom .icon .tb>div{display: table-cell; vertical-align: top; height: 100%; padding-right: 20px;}
.home-bottom .icon .tb .hd{text-align: left;}
.home-bottom .icon .tb p{margin-bottom: 10px;}
.home-bottom .icon .tb a{display: inline-block; padding: 3px 10px; border-radius: 3px; border: 1px solid #454545; margin-right: 10px;}
.home-bottom .icon .tb a:hover{color: #000000; background-color: #858585;}
.home-bottom .icon .line{display: inline-block; width: 0; height: 100%; margin: 0 15px; border-left: 1px dashed #454545;}
.home-bottom .icon .blink:hover .big{opacity: 1;}
.home-bottom .icon .tell{display: inline-block; width: 18px; height: 18px; background: url(../svg/tell.svg) no-repeat; background-size: 100% auto;}
.home-bottom .icon .fk{display: inline-block; vertical-align: middle; width: 10px; height: 10px; background: url(../svg/fk.svg) no-repeat; background-size: 100% auto; margin-right: 5px;}
.home-bottom hr{border: none; border-top: 1px solid #454545; margin: 20px 0;}
.home-bottom .link{margin-bottom: 20px;}
.home-bottom .link span{display: inline-block; vertical-align: middle; margin: 0 10px; padding-left: 12px; position: relative;}
.home-bottom .link span::before{content: ''; position: absolute; width: 8px; height: 8px; left: 0; top: calc(50% - 4px); background: url(../svg/dsj.svg) no-repeat; background-size: 100% auto;}
.home-bottom .link i{display: inline-block; width: 0; border-left: 1px solid #454545; height: 12px; vertical-align: middle; }
.home-bottom .jk{font-size: 0; margin-bottom: 20px;}
.home-bottom .jk span{display: inline-block; vertical-align: middle; margin: 0 10px; font-size: 12px;}
.footer{ font-size: 12px; background-color: #000000; background-image: url(../images/bg_b.jpg); background-repeat: repeat-x; height: 200px; font-size: 0; text-align: center;}
.footer *{font-family: simsun,Arial, Helvetica, sans-serif;}
.footer .content{height: 100%; margin: auto; width: calc(100% - 100px);}
.footer .content>div{display: inline-block; vertical-align: top; height: 100%; padding: 20px 10px; font-size: 12px; text-align: left;}
.footer .content p{line-height: 22px;}
.footer .content a{font-size: 12px; padding: 0;}
.footer .slice{width: 15px; background: url(../images/slice_b.jpg) no-repeat 0 0;}
.footer .content .logo-box{padding-right: 30px;}
.footer .logo-box img{max-height: 100px; max-width: 300px;}


.child-bg{position: fixed; z-index: -1; left: 0; top: 0; width: 100%;}
.news-page{margin-top: 280px; margin-bottom: 150px; background-color: #18191E;}
.news-page .title{padding: 20px; background-color: #0F0F12;}
.news-page .title span{display: inline-block; }
.news-page .title i{display: inline-block; vertical-align: middle; font-family: simsun; font-size: 12px; margin: 0 10px; font-style: normal; color: #454545;}
.news-page .list{padding: 30px;}
.news-page .middle-list{border-top: 3px solid #3F3F47;}
.news-page a{width: 100%;}
.news-page .item{height: 136px; padding: 20px 0; border-bottom: 1px solid #3F3F47; font-size: 0; transition: all .3s;}
.news-page .item:hover{background-color: #0F0F12;}
.news-page .item>div{display: inline-block; vertical-align: top; height: 100%;}
.news-page .item .img{width: 180px; margin-right: 20px;}
.news-page .item .img div{width: 100%; height: 100%;}
.news-page .item .img img{width: 100%; height: 100%;}
.news-page .item .context{width: calc(100% - 200px); padding-right: 50px;}
.news-page .item .context h2{font-size: 16px; margin-bottom: 10px; color: #ffffff;}
.news-page .item .context div{height: 50px; font-size: 14px; color: #787c8a;}
.news-page .item .context p{font-size: 12px; color: #484a58; overflow: hidden;}
.news-page .item .context .right{float: right; width: 21px; height: 6px; background: url(../images/hnewicon.png) no-repeat;}
.news-page .bottom-page{padding-bottom: 80px; padding-top: 50px; text-align: center; }
.news-page .bottom-page .more{background: url(../images/common2.png) no-repeat; background-position: -366px 0; width: 183px; height: 47px;}
.news-page .bottom-page .more:hover{background-position: -366px -47px;}
.news-page .head-area a{display: inline;}
.news-page .head-area{font-size: 0; margin-bottom: 30px; position: relative; height: 390px;}
.news-page .head-area div{display: inline-block; border: 2px solid #3F3F47; position: absolute; transition: all .3s;}
.news-page .head-area div:hover{transform: scale(1.05); z-index: 1;}
.news-page .head-area img{width: 100%; height: 100%;}
.news-page .head-area .big{width: calc(50% - 10px); height: 200px;}
.news-page .head-area .big1{left: 0; top: 0;}
.news-page .head-area .big2{right: 0; bottom: 0;}
.news-page .head-area .small{width: calc(25% - 15px); height: 170px;}
.news-page .head-area .small1{left: calc(50% + 10px); top: 0;}
.news-page .head-area .small2{right: 0; top: 0;}
.news-page .head-area .small3{left: 0; bottom: 0;}
.news-page .head-area .small4{left: calc(25% + 5px); bottom: 0;}
.news-page .news-content{padding: 0 50px; color: #C0C3D1;}
.news-page .tab-title{text-align: center;}
.news-page .tab-title h1{margin: 20px 0;}
.news-page .tab-title p{color: #54545e; font-size: 12px;}
.news-page .news-content>hr{margin: 30px 0; border: none; border-top: 1px solid #484a58;}
.news-page .news-content>.content{padding: 0 50px; font-size: 16px; padding-bottom: 100px;}
.news-page .news-content a{color: #ffa600;}

.rank-content{padding: 0 20px; color: #C0C3D1;}
.rank-top{padding: 20px 0;}
.rank-top .top-title{font-size: 38px; font-weight: bold; text-align: center; letter-spacing: 10px;}
.rank-top .line{border: none; border-top: 1px solid #454545; margin: 20px 0;}
.rank-table{width: 100%; border-collapse: collapse;}
.rank-table th,.rank-table td{height: 35px; padding: 3px 5px; border: 1px solid #373b49; text-align: center;}
.rank-table th{background-color: #25262e;}
.rank-table tr:hover td{background-color: #25262e;}
.icon-rank{display: inline-block; vertical-align: middle; width: 32px; height: 32px; background-position: 50% 50%; background-size: 100% auto;}
.icon-rank-1{background-image: url(../svg/rank-1.svg);}
.icon-rank-2{background-image: url(../svg/rank-2.svg);}
.icon-rank-3{background-image: url(../svg/rank-3.svg);}
.rank-help{margin: 10px 0; padding-bottom: 50px;}
.rank-table .sex{display: inline-block; width: 26px; height: 26px; vertical-align: middle; background-size: 100% auto; background-position: center center; background-repeat: no-repeat;}
.rank-table .women{background-image: url(../svg/women.svg);}
.rank-table .man{background-image: url(../svg/man.svg);}

.down-content{padding: 20px; color: #adafbb;}
.down-content a{color: #409cca; width: unset;}
.down-title{height: 45px; border-bottom: 1px solid #3F3F47; color: #A2A5B4; font-size: 18px; font-weight: bold;}
.down-title p{display: inline-block; height: calc(100% + 2px); border-bottom: 3px solid #3F3F47;}
.down-title .ico{display: inline-block; width: 15px; height: 14px; vertical-align: middle; background: url(../images/downicon.png) no-repeat center center;}
.down-title span{display: inline-block; vertical-align: middle; margin-left: 10px;}
.down-box{padding: 20px 0; text-align: center; margin-bottom: 30px;}
.down-content .down-button{display: inline-block; width: unset; height: 80px; padding: 0 50px; line-height: 80px; font-size: 20px; font-weight: bold; color: #ffffff; background: linear-gradient(to right, #3D4D99, #3789B2, #CB7A2A);}
.down-content .down-button span{display: inline-block; vertical-align: middle;}
.down-content .down-button i{display: inline-block; vertical-align: middle; width: 26px; height: 26px; background: url(../svg/download.svg) no-repeat center center; background-size: 100% auto; margin-left: 10px;}
.down-link a{margin: 0 20px; text-decoration: underline;}

.down-table{width: 100%; border-collapse: collapse;}
.down-table th,.down-table td{text-align: center; padding: 5px;}
.down-table th{height: 45px; background-color: #313239; font-size: 16px;}
.down-table td{height: 50px; border-bottom: 1px dashed #2e2f36; color: #727681;}
.down-table a{display: inline; text-decoration: underline;}
.md5-box{overflow: hidden;}
.md5-box-hide{height: 190px;}
.md5-more{height: 25px; line-height: 25px; text-align: center; background-color: #28282E; cursor: pointer;}
.md5-more:hover{color: #ececec;}
.down-tool{font-size: 0; overflow: hidden; text-align: left;}
.down-tool>div{display: inline-block; width: calc(100% / 4); padding-right: 15px; padding-bottom: 20px;}
.down-tool a{display: inline-block; height: 45px; line-height: 45px; width: 100%; border-radius: 3px; border: 1px solid #313239; position: relative; padding-left: 45px; font-size: 17px; color: #646779;}
.down-tool a:hover{border: 1px solid #737583;}
.down-tool a i{display: inline-block; width: 32px; height: 32px; position: absolute; left: 5px; top: calc(50% - 16px); background-position: center; background-repeat: no-repeat; background-size: 100% auto;}
.down-tool a .md5{background-image: url(../images/d-md5.jpg);}
.down-tool a .dx{background-image: url(../images/d-dx.jpg);}
.down-tool a .nvidia{background-image: url(../images/d-nvidia.jpg);}
.down-tool a .ati{background-image: url(../images/d-ati.jpg);}
.down-tool a .vc{background-image: url(../images/d-vc.jpg);}

.version-content{padding: 20px;}
.version-content .ver-title{font-size: 0; border-bottom: 1px solid #3F3F47; text-align: center; margin-bottom: 50px;}
.version-content .ver-title div{display: inline-block; height: 60px; line-height: 60px; padding: 0 20px; position: relative; font-size: 18px; color: #3A3F47; cursor: pointer;}
.version-content .ver-title div::before{content: ''; position: absolute; left: 50%; bottom: -1px; width: 0; transition: all .3s; height: 2px; background-color: #79CAF2;}
.version-content .ver-title div:hover::before{left: 0; width: 100%;}
.version-content .ver-title .current{color: #ffffff;}
.version-content .ver-title .current::before{left: 0; width: 100%;}
.update-record{width: 700px; margin: auto; border: 1px solid #3F3F47; overflow: hidden;}
.ver-img{width: 100%; height: 188px; background-color: #1A1A1A;}
.ver-img img{width: 100%; height: 100%;}
.update-record .version{border-top: 1px solid #3F3F47; margin-top: -1px; position: relative; z-index: 1;}
.update-record .version a{height: 42px; line-height: 42px; padding: 0 20px; position: relative;}
.update-record .version a::before{content: ''; position: absolute; width: 16px; height: 16px; right: 20px; top: calc(50% - 8px); background: url(../svg/right-2.svg) no-repeat center center; background-size: 100% auto; transition: all .4s;}
.update-record .version a:hover::before{right: 13px;}
.update-record .ver-link a{color: #4fbcf3; text-align: center;}
.update-record .ver-link a:hover{color: #70cfff;}
.update-record .ver-link a::before{content: unset;}
.update-record .ver-hide{display: none;}
