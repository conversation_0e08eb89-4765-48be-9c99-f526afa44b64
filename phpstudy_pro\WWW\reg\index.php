﻿<?php

/**
 * 这是单个页面的，所以数据库信息单独写了
 * 
 */

require_once dirname(__FILE__) . "/access/module/common.php";
require_once dirname(__FILE__) . "/access/module/verifyCode.php";
if(isPost()){
    $arr = $_POST;
    if(!isset($arr['token'])) die_json('fail', '缺少token参数');
    $tk_arr = arr_decry($arr['token']);
    if(!isset($tk_arr['TYPE'])) die_json('fail', 'token错误');

    switch ($tk_arr['TYPE']){
        case 'CREATE_USER' : // 创建账号
            if(!isset($arr['username'])) die_json('fail', '缺少用户名参数');
            if(!isset($arr['password'])) die_json('fail', '缺少密码参数');
            if(!isset($arr['repassword'])) die_json('fail', '缺少重复密码参数');
            if(!isset($arr['email'])) die_json('fail', '缺少电子邮件参数');
            if(!isset($arr['vcode'])) die_json('fail', '缺少验证码参数');
            if(!vcodeCheck($arr['vcode'])) die_json('fail', '验证码错误或已过期');

            if(!preg_match("/^[a-zA-Z0-9]{3,16}$/", $arr['username'])) die_json('fail', '账号格式错误');
            if(!preg_match("/^[a-zA-Z0-9]{3,16}$/", $arr['password'])) die_json('fail', '密码格式错误');
            if($arr['repassword'] != $arr['password']) die_json('fail', '重复输入密码错误');    // 如果触发这个，可以断定是有人调试或者尝试找漏洞
            if(!preg_match("/\w+([-+.]\w+)*@\w+([-.]\w+)*\.\w+([-.]\w+)*/", $arr['email'])) die_json('fail', '电子邮件地址不符合规则');

            // 链接数据库开始注册了，因为这里是调用存储过程且带有输出参数，所以不能用传统的mysql，要使用mysqli
            $mysqli = new mysqli("127.0.0.1", "root", "", "tos_lobby");   // 数据库账号密码在这里修改
            if(!$mysqli) die_json('fail', '服务器错误！');
            
            $ChannelCode = '';
            if(isset($arr['tgc'])){
                if(strlen($arr['tgc']) >= 1 && strlen($arr['tgc']) <= 30){
                    if(preg_match("/^[a-zA-Z0-9]{1,30}$/", $arr['tgc'])){
                        $ChannelCode = $arr['tgc'];
                    }else{
                        die_json('fail','无效渠道编号。');
                    }
                }else{
                    die_json('fail','渠道编号长度错误。');
                }
                
            }
            // 得到最大索引值
            $sql = "SELECT MAX(account_idx) AS account_idx FROM account;";
            if(!$mysqli->multi_query($sql)) die_json('fail', '查询错误！错误：1');
            if(!$res = $mysqli->store_result()) die_json('fail', '查询错误！错误：2');
            $row = $res->fetch_assoc();
            $id = (int)$row['account_idx'];
            $id++;
            $res->close();
            $proc = "CALL sp_account_create(%d, '%s', '%s', 3, 3, '%s', '%s', '%s', @outErr);";
            $proc = sprintf($proc, $id, $arr['username'], md5($arr['password']), $arr['email'], get_real_ip(), $ChannelCode);
            if(!$mysqli->query($proc)) die_json('fail', '查询错误！错误：3');
            $sql = "SELECT @outErr AS ErrorCode;";
            $que = $mysqli->query($sql);
            $res = $que->fetch_assoc();
            $que->close();
            if($res['ErrorCode'] == 0){
                $err = '注册成功！';
                die_json('success', $err);
            }elseif($res['ErrorCode'] == 1){
                $err = '注册失败！索引重复！';
            }elseif($res['ErrorCode'] == 2){
                $err = '此账号已存在！';
            }elseif($res['ErrorCode'] == 3){
                $err = '渠道编号不存在！';
            }
            die_json('fail', $err);
            
        default :
            die_json('fail', '请求异常！');
    }

}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $GLOBALS['WebTitle'] ?> - 注册账号</title>
    <link rel="stylesheet" href="css/main.css">
    <link rel="stylesheet" href="css/all.min.css">
    <script src="js/jquery-3.5.1.min.js"></script>
    <script src="js/style.js"></script>
    <style>
        html{background-color: #0F0F12;}
        .reg-box{padding: 20px; width: 350px; margin: auto;}
        .reg-title{text-align: center; font-size: 32px; margin: 20px 0; padding-bottom: 20px; border-bottom: 1px solid #292b33;}
        .reg-box *{outline: none;}
        .reg-box .input{margin-bottom: 42px; position: relative;}
        .reg-box .tips{position: absolute; left: 0; top: 38px; color: #414450; transition: all .2s;}
        .reg-box .tips-hide{transform: rotateX(90deg); opacity: 0;}
        .reg-box .tips-err{color: #d42525; padding-left: 18px;}
        .reg-box .tips-err::before{content: '×'; font-family: simsun; font-size: 12px; width: 14px; height: 14px; background-color: #d42525; color: #ffc1c1; border-radius: 50%; text-align: center; padding-top: 1px; padding-left: 1px; left: 0; top: 3px; position: absolute;}
        .reg-box input{height: 35px; width: 100%; padding: 0 10px; background-color: #0F0F12; color: #cbcbe9; border: 1px solid #30323b; vertical-align: middle;}
        .reg-box input:hover{ border: 1px solid #424450;}
        .reg-box input:focus{ border: 1px solid #898da5;}
        .reg-box .vcode input{width: calc(100% - 115px);}
        .reg-box .vcode img{width: 100px; height: 35px; vertical-align: middle; margin-left: 15px;}
        .reg-box button{height: 35px; width: 100%; border: none; background-color: #d42525; color: #ffffff;}
        .reg-box button:hover{background-color: #f54444;}
        .reg-box button:active{background-color: #ad1c1c; color: #e48989;}
        .reg-box button:disabled{opacity: .3; cursor: no-drop; background-color: #d42525; color: #ffffff;}
        .reg-box .reset{border: 1px solid #30323b; background-color: transparent; color: #898da5; margin-top: 20px;}
        .reg-box .reset:hover{background-color: transparent; border: 1px solid #898da5; color: #cbcbe9;}
        .reg-box .reset:active{background-color: #0F0F12; border: 1px solid #30323b; color: #424450;}
        .agree{color: #424450; text-align: center;}
    </style>
</head>
<body>
    <div class="child-bg"><img src="images/newlistbg.jpg" alt=""></div>

    <div class="news-page web">
        <div class="title"><span><a href="/main">首页</a></span><i>&gt;</i><span>注册账号</span></div>
        <div class="down-content">
            <div class="reg-title">免费创建您的游戏账号</div>
            <div class="reg-box">
                <form action="##" method="post" onsubmit="return sendData()">
                    <?php
                        if(isset($_GET['direction'])){
                            if(preg_match("/^[a-zA-Z0-9]{1,30}$/", $_GET['direction']) && strlen($_GET['direction']) <= 30){
                                echo '<input type="hidden" name="tgc" value="' . $_GET['direction'] . '">';
                            }
                        }
                    ?>
                    
                    <input type="hidden" name="token" value="<?php echo arr_encry(array("TYPE" => 'CREATE_USER')) ?>">
                    <div class="input">
                        <div><input id="username" name="username" type="text" maxlength="20" placeholder="游戏账号由3-16个英文和数字组合" autocomplete="off" autofocus></div><div class="tips">请输入游戏账号！</div>
                    </div>
                    <div class="input">
                        <div><input id="password" name="password" type="password" maxlength="20" placeholder="登录密码由3-16个英文和数字组合" autocomplete="new-password"></div><div class="tips">请输入登录密码！</div>
                    </div>
                    <div class="input">
                        <div><input id="repassword" name="repassword" type="password" maxlength="20" placeholder="重复输入密码确认" autocomplete="new-password"></div><div class="tips">请再次输入登录密码确认！</div>
                    </div>
                    <div class="input">
                        <div><input id="email" name="email" type="text" maxlength="50" placeholder="常用电子邮箱地址" autocomplete="off"></div><div class="tips">请填写您常用的电子邮箱地址！</div>
                    </div>
                    <div class="input vcode">
                        <div><input id="vcode" name="vcode" type="text" maxlength="10" placeholder="图中计算的结果" autocomplete="off" class=""><img src="vr-code.php?v=1.0" alt="" title="看不清？点击刷新" onclick="reflushVcode(this)"></div><div class="tips">请计算出图片中算式的结果后并填写！</div>
                    </div>
                    <div>
                        <button id="send">立即免费创建</button>
                        
                        <button type="button" class="reset">重置表单</button>
                    </div>
                    <br>
                    <div class="agree">注册即表示接受并同意<a href="" target="_blank">《用户服务协议》</a></div>
                </form>
            </div>
        </div>
    </div>
    <div class="footer">
        <div class="content">
            <div class="logo-box">
                <img src="images/logo.png" alt="">
            </div>
            <div class="slice"></div>
            <div>
                
                <p>健康游戏忠告：抵制不良游戏，拒绝盗版游戏；注意自我保护，谨防受骗上当；适度游戏益脑，沉迷游戏伤身；合理安排时间，享受健康生活。</p>
                <p>适龄提示：本游戏仅适合年满16+周岁的用户体验娱乐，游戏中可能存在部分打斗、血腥暴力等情节仅为游戏需要，请勿在现实生活中模仿。</p>
                <p>LOGO ILLUSTRATION: © 2010, 2014, 2016, 2018, 2021 173TOS,173GAME</p>
            </div>
            <div>
                <img src="images/dzyyzz.png" alt="">
                <img src="images/jzjh.png" alt="">
            </div>
        </div>
    </div>
    <script>
        $(document).ready(function(){

        }).on("click",".reset",function(){
            if(confirm("确定要重置表单重新填写吗？")){
                $("form input").val('');
                setTips($("#username"), false, '请输入游戏账号！');
                setTips($("#password"), false, '请输入登录密码！');
                setTips($("#repassword"), false, '请再次输入登录密码确认！');
                setTips($("#email"), false, '请填写您常用的电子邮箱地址！');
                setTips($("#vcode"), false, '请计算出图片中算式的结果后并填写！');
                $(".vcode img").click();
            }
        });
        function reflushVcode(ele){
            var url = 'vr-code.php?v=' + Math.random();
            $(ele).attr("src", url);
        }
        function sendData(){
            var ok = true;
            if(!/^[a-zA-Z0-9]{3,16}$/.test($("#username").val())){
                setTips($("#username"), true, '请填写游戏账号或格式错误！');
                ok = false;
            }else{
                setTips($("#username"), false, '填写正确！');
            }
            if(!/^[a-zA-Z0-9]{3,16}$/.test($("#password").val())){
                setTips($("#password"), true, '请填写登录密码或格式错误！');
                ok = false;
            }else{
                setTips($("#password"), false, '填写正确！');
            }
            if($("#repassword").val() != $("#password").val() || !$.trim($("#repassword").val())){
                setTips($("#repassword"), true, '重复填写登录密码错误！');
                ok = false;
            }else{
                setTips($("#repassword"), false, '填写正确！');
            }
            if(!/\w+([-+.]\w+)*@\w+([-.]\w+)*\.\w+([-.]\w+)*/.test($("#email").val())){
                setTips($("#email"), true, '请填写正确的电子游戏地址。');
                ok = false;
            }else{
                setTips($("#email"), false, '填写正确！');
            }
            if(!$.trim($("#vcode").val())){
                setTips($("#vcode"), true, '请计算出右侧图片中显示算式的结果并填写。');
                ok = false;
            }else{
                setTips($("#vcode"), false, '填写正确！');
            }
            if(!ok) return false;
            var text = $("#send").text();
            $("#send").text('正在努力为您创建，请稍后...');
            $("#send").attr("disabled", true);
            $.ajax({
                url:'',
                type:'post',
                dataType:'json',
                data:$("form").serializeArray(),
                success:function(data){
                    if(data.return_code != 'success'){
                        $("#send").text(text);
                        $("#send").attr("disabled", false);
                        $(".vcode img").click();
                        alert(data.return_msg);
                    }else{
                        alert(data.return_msg);
                        location.reload();
                    }
                },
                error:function(x,i,e){
                    $("#send").text(text);
                    $("#send").attr("disabled", false);
                    alert(e);
                }
            })
            return false;
        }
        function setTips(ele,fail,text){
            var pr = ele.parent().parent();
            pr.find(".tips").remove();
            if(fail){
                var html = '<div class="tips tips-err tips-hide">' + text + '</div>';
            }else{
                var html = '<div class="tips">' + text + '</div>';
            }
            pr.append(html);
            setTimeout(function(){
                pr.find(".tips").removeClass("tips-hide");
            },10);
        }
    </script>
</body>
</html>