var hMainTimer;
$(document).ready(function(){

}).on("click",".warp .btn-push-down",function(){
    if($(this).is(".btn-push-down-current")){
        $(this).removeClass("btn-push-down-current");
        $(".hide-area .detail").removeClass("detail-show");
    }else{
        $(this).addClass("btn-push-down-current");
        $(".hide-area .detail").addClass("detail-show");
    }
}).on("mouseenter",".warp .menu-list",function(){
    clearTimeout(hMainTimer);
    $(".warp").addClass("warp-show");
}).on("mouseleave",".warp",function(){
    hMainTimer = setTimeout(function(){
        $(".warp").removeClass("warp-show");
    },50);
}).on("mouseenter",".warp .menu dd",function(){
    $(this).parent().find("dt").addClass("current");
}).on("mouseleave",".warp .menu dd",function(){
    $(this).parent().find("dt").removeClass("current");
}).on("click",".home-slider .lb",function(){
    if($(this).is(".prev")){
        setSlider($(this).parent(), false);
    }else{
        setSlider($(this).parent(), true);
    }
    
});

var index = 0;
function setSlider(parent, direction){
    var all = parent.find(".item").length;
    if(all > 0){
        if(direction == false){
            // 往左边
            index--;
        }else{
            // 往右边
            index++;
        }
        if(index < 0){
            index = all - 1;
        }else if(index >= all){
            index = 0;
        }
        parent.find(".box").css({"left": - (index * parent.find(".item").width())+"px"})
    }
    
}