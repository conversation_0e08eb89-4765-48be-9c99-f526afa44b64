<?php
 
error_reporting(E_ALL & ~E_NOTICE);
// 新版的验证码，采用运算方式，这样能杜绝部分打码平台
$w = 100;
$h = 35;
$upper = array('零', '一', '二', '三', '四', '五', '六', '七', '八', '九');
$upper_ex = array('零', '壹', '贰', '叁', '肆', '伍', '陆', '柒', '捌', '玖');
$dot_ex = array('加', '减', '乘');
$type = rand(0, 2);
$num1 = rand(0, 9);  // 十以内加减乘和百以内加减乘对打码平台来说是一回事，但考虑用户体验只搞十以内加减乘，PS:除法就不用了，毕竟小数点用户还得拿计算器按。
$num2 = rand(0, 9);

if($type == 0){   // 加法
   $ret = $num1 + $num2;
   $dot = '+';
}elseif($type == 1){   // 减法
   $ret = $num1 - $num2;
   $dot = '-';
}elseif($type == 2){   // 乘法
   $ret = $num1 * $num2;
   $dot = '×';
}
// 将结果存入session
session_start();
$_SESSION['CACHE_VCODE_RET'] = $ret;
$_SESSION['CACHE_VCODE_TIME'] = time();

$str = Array();
$str[] = (rand(0,1) == 0 ? $upper[$num1] : (rand(0, 1) == 0 ? $upper_ex[$num1] : $num1));
$str[] = $dot_ex[$type]; //(rand(0,1) == 0 ? $dot : $dot_ex[$type]);
$str[] = (rand(0,1) == 0 ? $upper[$num2] : (rand(0, 1) == 0 ? $upper_ex[$num2] : $num2));

$im = imagecreatetruecolor($w,$h);
$white = imagecolorallocate($im,236,225,200); 
imagefilledrectangle($im,0,0,$w,$h,$white);
//将验证码写入图案
for($i = 0;$i < count($str);$i++){
   $x = 10 + $i * ($w - 10)/3;
   $y = 2+mt_rand(3,$h / 3);
   $color = imagecolorallocate($im,rand(0,150),rand(0,150),rand(0,150));
   $font_size=rand(16,22);	//字体大小
   $wing=rand(-50,50);	//旋转角度
   $rand_num = rand(0, 3);
   if($rand_num == 0){
      $font= dirname(__FILE__) . '/fonts/jtnc.ttf';
   }elseif($rand_num == 1){
      $font= dirname(__FILE__) . '/fonts/mnjsd.ttf';
   }elseif($rand_num == 2){
      $font= dirname(__FILE__) . '/fonts/stnh.ttf';
   }elseif($rand_num == 3){
      $font= dirname(__FILE__) . '/fonts/hyxrjt.ttf';
   }
   imagettftext($im, $font_size, $wing,$x,$h/2.5+$y, $color, $font, $str[$i]);
}
imagesetthickness ($im,1);
header("Content-type:image/jpeg"); //以jpeg格式输出，注意上面不能输出任何字符，否则出错
imagejpeg($im);
imagedestroy($im);
?>