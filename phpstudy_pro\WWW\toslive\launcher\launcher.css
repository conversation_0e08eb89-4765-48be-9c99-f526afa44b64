body, * { margin:0; padding:0; }
a img { border:0; }

html { overflow: hidden; width: 1000px; height: 505px; }
body { background:black; font:11px Arial; color:#a5bfc7; background-repeat:no-repeat; overflow: hidden; width: 1000px; height: 505px; border:none; }

.logo { position:absolute; display:block; margin:-0px 0 0 860px; padding:30px 0px 0 0;   }

.news_box {position:absolute;top:120px;left:400px;width:445px}
.news {position:relative; width:445px;line-height:15px;}
.news {position:relative; top:0px;}
.news .title {position:absolute;top:-51px;left:0;width:445px; height:80px;z-index:1;}
.news .contents {position:relative;top:0;left:35px;width:410px; z-index:10000;}
a { color:#f3e51c; text-decoration:none; -moz-outline:none; }
a:hover { color:white !important; }
a:visited { color:#f3e51c; }

.section_icon { float:left;width:40px; margin-right:8px;}
.items { float:left; width:320px;}
.item {  padding-left:8px; font-size: small; }
.date { float:right; }

.tupian{ border:1px solid #000; width:130px;} 
.tupian img{width:130px;}

.miaov_head{height:36px;width:100%;margin:0 auto;background: rgb(146, 145, 145);margin-bottom: 0px;}
.miaov_head  img{width: 30px ;height: 30px;margin-top: 0;margin-left: 130px;}
.miaov_head  ul{float: left;width:900px;height: 36px;margin-top: 0px;color: white;position: absolute;top: 0px;margin-left: 200px;}
.miaov_head  ul li{float: left;padding-left: 80px;margin-left: 0px;color: white;list-style: none; }
.miaov_head  ul li a{color: white;font-size: 14px;text-decoration: none;}
.miaov_head input{position: absolute;top: 5px;margin-left: 1000px;width: 200px;height: 22px;}
.miaov_head a{line-height:36px;color:#777;}
.miaov_head a:hover{color:#555;}


.top{
    /* 设置宽度高度背景颜色 */
    height: auto; /*高度改为自动高度*/
    width:100%;
    margin-left: 0;
    background:rgb(73, 72, 72, 0.8);
    position: fixed; /*固定在顶部*/
    top: 0;/*离顶部的距离为0*/
    margin-bottom: 5px;
}
.top ul{
    /* 清除ul标签的默认样式 */
    width: auto;/*宽度也改为自动*/
    list-style-type: none;
    white-space:nowrap;
    overflow: hidden;
    margin-left: 30%;
    /* margin-top: 0;          */
    padding: 0;
 
}
.top li {
    float:left; /* 使li内容横向浮动，即横向排列  */
    margin-right:2%;  /* 两个li之间的距离*/
    position: relative;
    overflow: hidden;
}
 
.top li a{
   /* 设置链接内容显示的格式*/
    display: block; /* 把链接显示为块元素可使整个链接区域可点击 */
    color:white;
    text-align: center;
    padding: 3px;
    overflow: hidden;
    text-decoration: none; /* 去除下划线 */
    font-size: medium;
}
.top li a:hover{
    /* 鼠标选中时背景变为黑色 */
    background-color: #111;
}
.top ul li ul{
    /* 设置二级菜单 */
    margin-left: -0.2px;
    background:rgb(189, 181, 181);
    position: relative;
    display: none; /* 默认隐藏二级菜单的内容 */
 
}
.top ul li ul li{
    /* 二级菜单li内容的显示 */
     
    float:none;
    text-align: center;
}
.top ul li:hover ul{
    /* 鼠标选中二级菜单内容时 */
    display: block;
}
