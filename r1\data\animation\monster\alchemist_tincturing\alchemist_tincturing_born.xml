<?xml version="1.0" encoding="EUC-KR"?>
<!-- edited with XMLSPY v2004 rel. 3 U (http://www.xmlspy.com) by youngt616 (IMCGames Co., Ltd) -->
<ActionDesc>
    <Shockwave frame="0" type="2" intensity="1" frequency="70" duration="0.3" range="1000"/>
    <Shockwave frame="2" type="2" intensity="1" frequency="70" duration="0.3" range="1000"/>
    <Shockwave frame="4" type="2" intensity="1" frequency="70" duration="0.3" range="1000"/>
    <Shockwave frame="6" type="2" intensity="1" frequency="70" duration="0.3" range="1000"/>
    <Sound frame="7.78" soundFile="skl_eff_alchemist_tincturing_born"/>
    <Particle frame="26" attachNode="Bone_brazier01" particleName="F_smoke109_2" useAttach="true" hiddenObj="false" changeEffect="None" scale="5" pause="true" use2DEffect="false" targetPos="false" sound="mon_spector_run"/>
    <Particle frame="27" attachNode="Bone_table01" particleName="F_smoke109_2" useAttach="true" hiddenObj="false" changeEffect="None" scale="5" pause="true" use2DEffect="false" targetPos="false" sound="mon_spector_run"/>
    <Particle frame="28" attachNode="Dummy_Bone_brazier" particleName="F_levitation034_smoke" useAttach="true" hiddenObj="false" changeEffect="None" scale="0.1" pause="true" use2DEffect="true" targetPos="false"/>
    <Particle frame="28" attachNode="Dummy_Bone_brazier01" particleName="F_wizard_BRIQUETTING_STEP1_fire" useAttach="true" hiddenObj="false" changeEffect="None" scale="0.5" pause="true" use2DEffect="true" targetPos="false"/>
    <Particle frame="30" attachNode="Dummy_Bone_brazier" particleName="F_levitation034_smoke" useAttach="true" hiddenObj="false" changeEffect="None" scale="0.1" pause="true" use2DEffect="true" targetPos="false"/>
    <Particle frame="30" attachNode="Dummy_Bone_brazier01" particleName="F_wizard_BRIQUETTING_STEP1_fire" useAttach="true" hiddenObj="false" changeEffect="None" scale="0.5" pause="true" use2DEffect="true" targetPos="false"/>
    <Particle frame="32" attachNode="Dummy_Bone_brazier" particleName="F_levitation034_smoke" useAttach="true" hiddenObj="false" changeEffect="None" scale="0.1" pause="true" use2DEffect="true" targetPos="false"/>
    <Particle frame="32" attachNode="Dummy_Bone_brazier01" particleName="F_wizard_BRIQUETTING_STEP1_fire" useAttach="true" hiddenObj="false" changeEffect="None" scale="0.5" pause="true" use2DEffect="true" targetPos="false"/>
    <Particle frame="33" attachNode="Bone_basket2" particleName="F_smoke109_2" useAttach="true" hiddenObj="false" changeEffect="None" scale="3" pause="true" use2DEffect="false" targetPos="false" sound="mon_spector_run"/>
    <Particle frame="34" attachNode="Bone_basket" particleName="F_smoke109_2" useAttach="true" hiddenObj="false" changeEffect="None" scale="3" pause="true" use2DEffect="false" targetPos="false" sound="mon_spector_run"/>
    <Particle frame="34" attachNode="Bone_testtube7" particleName="F_smoke109_2" useAttach="true" hiddenObj="false" changeEffect="None" scale="3" pause="true" use2DEffect="false" targetPos="false" sound="mon_spector_run"/>
    <Particle frame="36" attachNode="Bone_bowlbody" particleName="F_smoke109_2" useAttach="true" hiddenObj="false" changeEffect="None" scale="3" pause="true" use2DEffect="false" targetPos="false" sound="mon_spector_run"/>
    <Particle frame="38" attachNode="Bone_testtube9" particleName="F_smoke109_2" useAttach="true" hiddenObj="false" changeEffect="None" scale="3" pause="true" use2DEffect="false" targetPos="false" sound="mon_spector_run"/>
    <Particle frame="39" attachNode="Bone_basket2" particleName="F_smoke109_2" useAttach="true" hiddenObj="false" changeEffect="None" scale="3" pause="true" use2DEffect="false" targetPos="false" sound="mon_spector_run"/>
    <Particle frame="43" attachNode="Bone_pot" particleName="F_smoke109_2" useAttach="true" hiddenObj="false" changeEffect="None" scale="3" pause="true" use2DEffect="false" targetPos="false" sound="mon_spector_run"/>
    <Particle frame="45" attachNode="Bone_brazier" particleName="F_smoke109_2" useAttach="true" hiddenObj="false" changeEffect="None" scale="3" pause="true" use2DEffect="false" targetPos="false" sound="mon_spector_run"/>
</ActionDesc>
