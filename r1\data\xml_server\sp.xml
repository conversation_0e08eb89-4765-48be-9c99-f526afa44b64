<?xml version="1.0" encoding="UTF-8"?>
<!-- edited with XMLSPY v2004 rel. 3 U (http://www.xmlspy.com) by imc (imc) -->
<idspace id="sp">
	<Category Name="Resource">
		<Class ClassID="1" ClassName="sp_server_info_get_1" ParamCount="2" Desc="ZServer"/>
		<Class ClassID="2" ClassName="sp_package_buy_set" ParamCount="4" Desc="ZServer"/>
		<Class ClassID="3" ClassName="sp_package_buy_get" ParamCount="2" Desc="ZServer"/>
		<Class ClassID="6" ClassName="sp_contents_shutdown_get" ParamCount="1" Desc="ZServer"/>
		<Class ClassID="7" ClassName="sp_contents_shutdown_add" ParamCount="2" Desc="ZServer"/>
		<Class ClassID="8" ClassName="sp_contents_shutdown_del" ParamCount="2" Desc="ZServer"/>
		<Class ClassID="9" ClassName="sp_contents_shutdown_del_all" ParamCount="1" Desc="ZServer"/>
		<Class ClassID="10" ClassName="sp_account_block_set" ParamCount="6" Desc="ZServer"/>
	</Category>
	<Category Name="Game">
		<Class ClassID="1000" ClassName="sp_pc_buff_set_1" ParamCount="10" Desc="ZServer"/>
		<Class ClassID="1100" ClassName="sp_pc_name_check" ParamCount="2" Desc="GServer"/>
		<Class ClassID="1101" ClassName="sp_pc_info_barrack_2" ParamCount="2" Desc="BServer"/>
		<Class ClassID="1102" ClassName="sp_pc_info" ParamCount="2" Desc="ZServer"/>
		<Class ClassID="1103" ClassName="sp_pc_quickslot_set" ParamCount="3" Desc="ZServer"/>
		<Class ClassID="1104" ClassName="sp_pc_map_set" ParamCount="3" Desc="BServer, ZServer"/>
		<Class ClassID="1105" ClassName="sp_pc_channel_set" ParamCount="2" Desc="BServer, ZServer"/>
		<Class ClassID="1200" ClassName="sp_pc_cooldown_set" ParamCount="4" Desc="ZServer"/>
		<Class ClassID="1300" ClassName="sp_ies_get" ParamCount="2" Desc="ZServer"/>
		<Class ClassID="1301" ClassName="RServerIESSet" ParamCount="5" Desc="BServer, ZServer"/>
		<Class ClassID="1302" ClassName="sp_ies_remove" ParamCount="4" Desc="ZServer"/>
		<Class ClassID="1400" ClassName="sp_pc_item_get_field_1" ParamCount="6" Desc="ZServer"/>
		<Class ClassID="1401" ClassName="sp_pc_item_buy_1" ParamCount="7" Desc="ZServer"/>
		<Class ClassID="1402" ClassName="sp_pc_item_drop_field" ParamCount="5" Desc="ZServer"/>
		<Class ClassID="1403" ClassName="sp_pc_item_equip" ParamCount="4" Desc="ZServer"/>
		<Class ClassID="1404" ClassName="sp_pc_item_exchange" ParamCount="9" Desc="ZServer"/>
		<Class ClassID="1406" ClassName="sp_pc_item_amount_decrease_1" ParamCount="3" Desc="ZServer"/>
		<Class ClassID="1408" ClassName="CharacterItemSpotChange" ParamCount="3" Desc="ZServer"/>
		<Class ClassID="1409" ClassName="sp_pc_item_unequip" ParamCount="6" Desc="ZServer"/>
		<Class ClassID="1410" ClassName="sp_pc_item_bag_slot_set" ParamCount="3" Desc="ZServer"/>
		<Class ClassID="1413" ClassName="sp_wiki_stt_get" ParamCount="0" Desc="GServer"/>
		<Class ClassID="1414" ClassName="sp_pc_rank_reward_season_remove" ParamCount="2" Desc="GServer"/>
		<Class ClassID="1415" ClassName="sp_pc_rank_reward_season_get" ParamCount="1" Desc="ZServer"/>
		<Class ClassID="1416" ClassName="sp_pc_rank_reward_add" ParamCount="5" Desc="GServer"/>
		<Class ClassID="1417" ClassName="sp_pc_icon_info_all_2" ParamCount="0" Desc="ZServer"/>
		<Class ClassID="1418" ClassName="sp_pc_icon_info_last_1" ParamCount="2" Desc="GServer"/>
		<Class ClassID="1419" ClassName="sp_pc_icon_info_1" ParamCount="1" Desc="GServer"/>
		<Class ClassID="1420" ClassName="sp_pc_apperance_info_2" ParamCount="2" Desc="GServer"/>
		<Class ClassID="1700" ClassName="sp_npc_add" ParamCount="2" Desc="ZServer"/>
		<Class ClassID="1701" ClassName="sp_npc_get" ParamCount="1" Desc="ZServer"/>
		<Class ClassID="1800" ClassName="sp_pc_looks_set" ParamCount="3" Desc="BServer, ZServer"/>
		<Class ClassID="1801" ClassName="CharacterPcExpUp" ParamCount="2" Desc="ZServer"/>
		<Class ClassID="1803" ClassName="sp_pc_connect_1" ParamCount="2" Desc="ZServer"/>
		<Class ClassID="1804" ClassName="sp_pc_disconnect" ParamCount="1" Desc="ZServer"/>
		<Class ClassID="1805" ClassName="sp_pc_save" ParamCount="7" Desc="ZServer"/>
		<Class ClassID="1807" ClassName="sp_pc_resurrect_item" ParamCount="5" Desc="ZServer"/>
		<Class ClassID="1808" ClassName="sp_pc_restart_place_set" ParamCount="6" Desc="ZServer"/>
		<Class ClassID="1900" ClassName="CharacterQuestGiveup" ParamCount="2" Desc="ZServer"/>
		<Class ClassID="1901" ClassName="CharacterQuestSetComplete" ParamCount="3" Desc="ZServer"/>
		<Class ClassID="1902" ClassName="CharacterQuestRemoveComplete" ParamCount="2" Desc="ZServer"/>
		<Class ClassID="1904" ClassName="CharacterQuestCompleteList" ParamCount="1" Desc="ZServer"/>
		<Class ClassID="1905" ClassName="CharacterQuestProgressList" ParamCount="1" Desc="ZServer"/>
		<Class ClassID="1906" ClassName="CharacterQuestRemoveRepeat" ParamCount="2" Desc="ZServer"/>
		<Class ClassID="1907" ClassName="CharacterQuestProgressSave" ParamCount="5" Desc="ZServer"/>
		<Class ClassID="1910" ClassName="CharacterQuestPropertySet" ParamCount="4" Desc="ZServer"/>
		<Class ClassID="1911" ClassName="CharacterQuestStart" ParamCount="3" Desc="ZServer"/>
		<Class ClassID="2000" ClassName="sp_pc_skill_add" ParamCount="3" Desc="ZServer"/>
		<Class ClassID="2001" ClassName="sp_pc_skill_remove_2" ParamCount="4" Desc="ZServer"/>
		<Class ClassID="2002" ClassName="sp_pc_ability_add" ParamCount="3" Desc="ZServer"/>
		<Class ClassID="2003" ClassName="sp_pc_ability_remove_1" ParamCount="3" Desc="ZServer"/>
		<Class ClassID="2004" ClassName="CharacterAbilityActiveSet" ParamCount="2" Desc="ZServer"/>
		<Class ClassID="2100" ClassName="sp_pc_session_object_add" ParamCount="3" Desc="ZServer"/>
		<Class ClassID="2101" ClassName="sp_pc_session_object_remove" ParamCount="2" Desc="ZServer"/>
		<Class ClassID="2102" ClassName="sp_pc_session_object_time_set" ParamCount="2" Desc="ZServer"/>
		<Class ClassID="2103" ClassName="sp_party_mission_his_set" ParamCount="5" Desc="ZServer"/>
		<Class ClassID="2104" ClassName="sp_party_mission_his_get" ParamCount="1" Desc="ZServer"/>
		<Class ClassID="2105" ClassName="sp_party_mission_his_remove" ParamCount="4" Desc="ZServer"/>
		<Class ClassID="5002" ClassName="RServerGMNotifyRemove" ParamCount="3" Desc="ZServer"/>
		<Class ClassID="5003" ClassName="RServerGMNotifyList" ParamCount="1" Desc="ZServer"/>
		<Class ClassID="5004" ClassName="RServerGMNotifySet" ParamCount="5" Desc="ZServer"/>
		<Class ClassID="10000" ClassName="RServerCharacterBanSet" ParamCount="1" Desc="BServer"/>
		<Class ClassID="10001" ClassName="RServerSessionTotalCount" ParamCount="1" Desc="GServer"/>
		<Class ClassID="10002" ClassName="sp_account_connect" ParamCount="5" Desc="BServer"/>
		<Class ClassID="10003" ClassName="sp_account_session_get" ParamCount="1" Desc="BServer, ZServer"/>
		<Class ClassID="10004" ClassName="RServerSessionGetByName" ParamCount="1" Desc="GServer"/>
		<Class ClassID="10005" ClassName="sp_account_connect_reset" ParamCount="6" Desc="BServer, GServer, ZServer"/>
		<Class ClassID="10006" ClassName="sp_account_session_set" ParamCount="6" Desc="BServer, ZServer"/>
		<Class ClassID="10007" ClassName="sp_account_session_auth" ParamCount="2" Desc="BServer, ZServer"/>
		<Class ClassID="30000" ClassName="sp_world_instance_number" ParamCount="0" Desc="ServerShared"/>
		<Class ClassID="30001" ClassName="sp_account_pass_check" ParamCount="3" Desc="BServer"/>
		<Class ClassID="40000" ClassName="sp_pc_create_1" ParamCount="15" Desc="BServer"/>
		<Class ClassID="40001" ClassName="sp_pc_delete_1" ParamCount="2" Desc="BServer"/>
		<Class ClassID="41002" ClassName="sp_party_found" ParamCount="5" Desc="GServer"/>
		<Class ClassID="41003" ClassName="sp_party_pc_join" ParamCount="4" Desc="GServer"/>
		<Class ClassID="41004" ClassName="sp_party_pc_leave" ParamCount="3" Desc="GServer"/>
		<Class ClassID="41006" ClassName="sp_party_list" ParamCount="1" Desc="GServer"/>
		<Class ClassID="41007" ClassName="sp_party_member_list" ParamCount="2" Desc="GServer"/>
		<Class ClassID="41008" ClassName="sp_party_leader_change" ParamCount="3" Desc="GServer"/>
		<Class ClassID="41009" ClassName="CharacterGuildCreate" ParamCount="3" Desc="GServer"/>
		<Class ClassID="41010" ClassName="CharacterGuildJoin" ParamCount="6" Desc="GServer"/>
		<Class ClassID="41011" ClassName="CharacterGuildList" ParamCount="0" Desc="GServer"/>
		<Class ClassID="41012" ClassName="CharacterGuildListByID" ParamCount="1" Desc="GServer"/>
		<Class ClassID="41013" ClassName="CharacterGuildOut" ParamCount="3" Desc="GServer"/>
		<Class ClassID="41014" ClassName="sp_pc_skill_respec" ParamCount="4" Desc="ZServer"/>
		<Class ClassID="41015" ClassName="sp_guild_found_1" ParamCount="5" Desc="GServer"/>
		<Class ClassID="41016" ClassName="sp_guild_pc_join" ParamCount="3" Desc="GServer"/>
		<Class ClassID="41017" ClassName="sp_guild_pc_leave" ParamCount="3" Desc="GServer"/>
		<Class ClassID="41018" ClassName="sp_guild_list_2" ParamCount="1" Desc="GServer"/>
		<Class ClassID="41019" ClassName="sp_guild_member_list" ParamCount="1" Desc="GServer"/>
		<Class ClassID="41020" ClassName="sp_guild_leader_change" ParamCount="3" Desc="GServer"/>
		<Class ClassID="41021" ClassName="sp_guild_prop_get" ParamCount="1" Desc="GServer"/>
		<Class ClassID="41022" ClassName="sp_ies_guild_set" ParamCount="5" Desc="BServer, ZServer"/>
		<Class ClassID="41023" ClassName="sp_guild_name_set_1" ParamCount="3" Desc="ZServer"/>
		<Class ClassID="41024" ClassName="sp_guild_notice_set" ParamCount="2" Desc="ZServer"/>
		<Class ClassID="41025" ClassName="sp_guild_profile_set" ParamCount="2" Desc="ZServer"/>
		<Class ClassID="41026" ClassName="sp_guild_grade_set" ParamCount="4" Desc="ZServer"/>
		<Class ClassID="41027" ClassName="sp_guild_info_1" ParamCount="1" Desc="ZServer"/>
		<Class ClassID="41028" ClassName="sp_guild_member_grade_change" ParamCount="4" Desc="ZServer"/>
		<Class ClassID="41029" ClassName="sp_guild_house_object_set" ParamCount="7" Desc="ZServer"/>
		<Class ClassID="41030" ClassName="sp_guild_house_get" ParamCount="1" Desc="ZServer"/>
		<Class ClassID="41031" ClassName="sp_guild_house_object_value_set" ParamCount="3" Desc="ZServer"/>
		<Class ClassID="41032" ClassName="sp_guild_house_object_remove" ParamCount="3" Desc="ZServer"/>
		<Class ClassID="41033" ClassName="sp_ies_master_list" ParamCount="0" Desc="GServer"/>
		<Class ClassID="41034" ClassName="sp_guild_member_prop_set" ParamCount="5" Desc="GServer"/>
		<Class ClassID="41035" ClassName="sp_guild_war_declare_2" ParamCount="5" Desc="GServer"/>
		<Class ClassID="41036" ClassName="sp_guild_war_remove" ParamCount="3" Desc="GServer"/>
		<Class ClassID="41037" ClassName="sp_guild_disband_1" ParamCount="3" Desc="GServer"/>
		<Class ClassID="41038" ClassName="sp_account_last_connect_ip_get" ParamCount="1" Desc="GServer"/>
		<Class ClassID="41039" ClassName="sp_pc_buff_clear" ParamCount="1" Desc="GServer"/>
		<Class ClassID="41040" ClassName="sp_pc_buff_remove" ParamCount="2" Desc="GServer"/>
		<Class ClassID="41041" ClassName="sp_cash_purchase_polling" ParamCount="0" Desc="GServer"/>
		<Class ClassID="41042" ClassName="sp_cash_purchase_gm_message" ParamCount="2" Desc="GServer"/>
		<Class ClassID="41043" ClassName="sp_cash_purchase_reg" ParamCount="6" Desc="ZServer"/>
		<Class ClassID="41044" ClassName="sp_cash_purchase_complete" ParamCount="3" Desc="ZServer"/>
		<Class ClassID="41045" ClassName="sp_market_price_avg_get_1" ParamCount="4" Desc="ZServer"/>
		<Class ClassID="41046" ClassName="sp_market_item_get" ParamCount="2" Desc="ZServer"/>
		<Class ClassID="41047" ClassName="sp_guild_colony_map_list" ParamCount="1" Desc="ZServer"/>
		<Class ClassID="41048" ClassName="sp_guild_colony_map_set" ParamCount="4" Desc="ZServer"/>
		<Class ClassID="41049" ClassName="sp_guild_colony_point_set" ParamCount="4" Desc="ZServer"/>
		<Class ClassID="41050" ClassName="sp_guild_colony_point_list" ParamCount="1" Desc="ZServer"/>
		<Class ClassID="41051" ClassName="sp_guild_colony_point_reset" ParamCount="1" Desc="ZServer"/>
		<Class ClassID="41052" ClassName="sp_guild_colony_boss_kill_set" ParamCount="4" Desc="GServer"/>
		<Class ClassID="41053" ClassName="sp_guild_colony_boss_kill_reset" ParamCount="1" Desc="GServer"/>
		<Class ClassID="41054" ClassName="sp_guild_colony_map_time_set" ParamCount="2" Desc="GServer"/>
		<Class ClassID="41055" ClassName="sp_guild_pc_leave_1" ParamCount="3" Desc="GServer"/>
		<Class ClassID="41056" ClassName="sp_guild_disband_2" ParamCount="3" Desc="GServer"/>
		<!-- 이후 DB 최적화 작업을 위해 추가되는 SP는 여기다가 넣을 것. -->
		<Class ClassID="50000" ClassName="sp_pc_item_add_equip" ParamCount="4" Desc="Bserver"/>
		<Class ClassID="50001" ClassName="CharacterMacroAdd" ParamCount="4" Desc="Zserver"/>
		<Class ClassID="50002" ClassName="CharacterMacroListAdd" ParamCount="5" Desc="Zserver"/>
		<Class ClassID="50003" ClassName="CharacterMacroRemove" ParamCount="2" Desc="Zserver"/>
		<Class ClassID="50004" ClassName="RServerCommanderSessionClear" ParamCount="1" Desc="BServer"/>
		<Class ClassID="50005" ClassName="sp_pc_ability_map_add" ParamCount="5" Desc="ZServer"/>
		<Class ClassID="50006" ClassName="sp_pc_achieve_point_set" ParamCount="5" Desc="ZServer"/>
		<Class ClassID="50007" ClassName="sp_pc_achieve_point_get" ParamCount="0" Desc="ZServer"/>
		<Class ClassID="50008" ClassName="sp_pc_achieve_add" ParamCount="2" Desc="ZServer"/>
		<Class ClassID="50010" ClassName="sp_pc_achieve_daily_remove" ParamCount="1" Desc="ZServer"/>
		<Class ClassID="50011" ClassName="sp_pc_option_set" ParamCount="2" Desc="ZServer"/>
		<Class ClassID="50012" ClassName="AchieveRankSave" ParamCount="5" Desc="ZServer"/>
		<Class ClassID="50013" ClassName="sp_pc_macro_chat_set" ParamCount="4" Desc="ZServer"/>
		<Class ClassID="50014" ClassName="sp_pc_macro_combo_set" ParamCount="5" Desc="ZServer"/>
		<Class ClassID="50015" ClassName="sp_account_map_fog_set" ParamCount="4" Desc="ZServer"/>
		<Class ClassID="50016" ClassName="CharacterUIInfoSet" ParamCount="10" Desc="ZServer"/>
		<Class ClassID="50017" ClassName="CharacterUIGroupDelete" ParamCount="2" Desc="ZServer"/>
		<Class ClassID="50018" ClassName="sp_pc_npc_stat_set" ParamCount="5" Desc="ZServer"/>
		<Class ClassID="50019" ClassName="sp_ies_history_get" ParamCount="1" Desc="ZServer"/>
		<Class ClassID="50020" ClassName="sp_ies_history_add" ParamCount="10" Desc="ZServer"/>
		<Class ClassID="50021" ClassName="sp_ies_history_remove" ParamCount="1" Desc="ZServer"/>
		<Class ClassID="50022" ClassName="sp_world_prop_set" ParamCount="2" Desc="ZServer"/>
		<Class ClassID="50023" ClassName="sp_world_prop_get" ParamCount="0" Desc="ZServer"/>
		<Class ClassID="50024" ClassName="sp_pc_item_equip_remove" ParamCount="4" Desc="ZServer"/>
		<Class ClassID="50025" ClassName="sp_pc_item_sell_2" ParamCount="6" Desc="ZServer"/>
		<Class ClassID="50026" ClassName="sp_pc_solditem_get" ParamCount="1" Desc="ZServer"/>
		<Class ClassID="50027" ClassName="sp_pc_solditem_buy_2" ParamCount="7" Desc="ZServer"/>
		<Class ClassID="50028" ClassName="sp_pc_solditem_remove_1" ParamCount="3" Desc="ZServer"/>
		<Class ClassID="50029" ClassName="sp_pc_warehouse_item_take" ParamCount="3" Desc="ZServer"/>
		<Class ClassID="50030" ClassName="sp_pc_warehouse_item_give" ParamCount="6" Desc="ZServer"/>
		<Class ClassID="50031" ClassName="sp_pc_item_warehouse_get" ParamCount="1" Desc="ZServer"/>
		<Class ClassID="50032" ClassName="sp_pc_exp_add_1" ParamCount="3" Desc="ZServer"/>
		<Class ClassID="50033" ClassName="CharacterUIPropSet" ParamCount="6" Desc="ZServer"/>
		<Class ClassID="50034" ClassName="CharacterUIPropDel" ParamCount="4" Desc="ZServer"/>
		<Class ClassID="50035" ClassName="sp_db_time_get" ParamCount="0" Desc="ZServer"/>
		<Class ClassID="50036" ClassName="CharacterRecipeSave" ParamCount="5" Desc="ZServer"/>
		<Class ClassID="50037" ClassName="CharacterRecipeDel" ParamCount="2" Desc="ZServer"/>
		<Class ClassID="50038" ClassName="SprayMonsterLoad" ParamCount="2" Desc="ZServer"/>
		<Class ClassID="50039" ClassName="SprayMonsterSave" ParamCount="10" Desc="ZServer"/>
		<Class ClassID="50040" ClassName="SprayMonsterTimeSave" ParamCount="2" Desc="ZServer"/>
		<Class ClassID="50041" ClassName="SprayMonsterDelete" ParamCount="1" Desc="ZServer"/>
		<Class ClassID="50042" ClassName="SprayLikeGet" ParamCount="1" Desc="ZServer"/>
		<Class ClassID="50043" ClassName="SprayLikeAdd" ParamCount="2" Desc="ZServer"/>
		<Class ClassID="50044" ClassName="SprayLikeDelete" ParamCount="2" Desc="ZServer"/>
		<Class ClassID="50045" ClassName="sp_pc_wiki_prop_hold_apply" ParamCount="4" Desc="ZServer"/>
		<Class ClassID="50046" ClassName="sp_pc_wiki_prop_int_hold_apply" ParamCount="3" Desc="ZServer"/>
		<Class ClassID="50052" ClassName="sp_pc_item_remove_1" ParamCount="4" Desc="ZServer"/>
		<Class ClassID="50053" ClassName="ReinforceGetList" ParamCount="1" Desc="ZServer"/>
		<Class ClassID="50054" ClassName="ReinforcePutItem" ParamCount="2" Desc="ZServer"/>
		<Class ClassID="50055" ClassName="ReinforceTakeItem" ParamCount="4" Desc="ZServer"/>
		<Class ClassID="50056" ClassName="sp_ies_item_set" ParamCount="5" Desc="ZServer"/>
		<Class ClassID="50057" ClassName="ReinforceTimeAdd" ParamCount="2" Desc="ZServer"/>
		<Class ClassID="50058" ClassName="sp_party_prop_get" ParamCount="1" Desc="ZServer"/>
		<Class ClassID="50059" ClassName="sp_party_item_list" ParamCount="1" Desc="ZServer"/>
		<Class ClassID="50060" ClassName="PartyItemGetField" ParamCount="6" Desc="ZServer"/>
		<Class ClassID="50061" ClassName="sp_party_item_take" ParamCount="8" Desc="ZServer"/>
		<Class ClassID="50062" ClassName="sp_party_item_put" ParamCount="8" Desc="ZServer"/>
		<Class ClassID="50063" ClassName="GiftItemToPC" ParamCount="4" Desc="ZServer"/>
		<Class ClassID="50064" ClassName="CharacterGetGiftItem" ParamCount="7" Desc="ZServer"/>
		<Class ClassID="50066" ClassName="sp_party_item_slot_set" ParamCount="2" Desc="ZServer"/>
		<Class ClassID="50067" ClassName="sp_party_survey_add" ParamCount="4" Desc="ZServer"/>
		<Class ClassID="50068" ClassName="sp_survey_ask_add" ParamCount="3" Desc="ZServer"/>
		<Class ClassID="50069" ClassName="sp_survey_ask_agree_add" ParamCount="3" Desc="ZServer"/>
		<Class ClassID="50070" ClassName="sp_party_survey_list" ParamCount="1" Desc="ZServer"/>
		<Class ClassID="50071" ClassName="sp_survey_ask_agree_get" ParamCount="1" Desc="ZServer"/>
		<Class ClassID="50072" ClassName="sp_party_exp_add" ParamCount="2" Desc="ZServer"/>
		<Class ClassID="50073" ClassName="IESListSet" ParamCount="3" Desc="ZServer"/>
		<Class ClassID="50074" ClassName="sp_party_item_remove" ParamCount="3" Desc="ZServer"/>
		<Class ClassID="50076" ClassName="sp_party_disband" ParamCount="1" Desc="ZServer"/>
		<Class ClassID="50077" ClassName="sp_pc_level_up_1" ParamCount="3" Desc="ZServer"/>
		<Class ClassID="50079" ClassName="sp_ies_ability_set" ParamCount="5" Desc="BServer, ZServer"/>
		<Class ClassID="50080" ClassName="sp_ies_item_set_" ParamCount="1" Desc="BServer, ZServer"/>
		<Class ClassID="50081" ClassName="sp_ies_party_set" ParamCount="5" Desc="BServer, ZServer"/>
		<Class ClassID="50082" ClassName="sp_ies_pc_set" ParamCount="5" Desc="BServer, ZServer"/>
		<Class ClassID="50083" ClassName="sp_ies_sessionobject_set" ParamCount="5" Desc="BServer, ZServer"/>
		<Class ClassID="50084" ClassName="sp_ies_skill_set" ParamCount="5" Desc="BServer, ZServer"/>
		<Class ClassID="50085" ClassName="sp_ies_companion_set" ParamCount="5" Desc="BServer, ZServer"/>
		<Class ClassID="50090" ClassName="sp_ies_item_set__" ParamCount="1" Desc="BServer, ZServer"/>
		<Class ClassID="50092" ClassName="CharacterSessionObjectTargetIDSet" ParamCount="2" Desc="BServer, ZServer"/>
		<Class ClassID="50094" ClassName="sp_ies_pc_set_" ParamCount="3" Desc="ZServer"/>
		<Class ClassID="50095" ClassName="CharacterWikiPropAdd_Int" ParamCount="4" Desc="ZServer"/>
		<Class ClassID="50096" ClassName="sp_pc_name_set" ParamCount="3" Desc="ZServer"/>
		<Class ClassID="50097" ClassName="sp_pc_job_exp_set_1" ParamCount="4" Desc="ZServer"/>
		<Class ClassID="50098" ClassName="sp_pc_job_set" ParamCount="4" Desc="ZServer"/>
		<Class ClassID="50099" ClassName="sp_pc_job_level_set_1" ParamCount="6" Desc="ZServer"/>
		<Class ClassID="50100" ClassName="sp_pc_skillpoint_decrease" ParamCount="3" Desc="ZServer"/>
		<Class ClassID="50101" ClassName="sp_pc_luck_point_set" ParamCount="4" Desc="ZServer"/>
		<Class ClassID="50102" ClassName="sp_ies_master_remove" ParamCount="0" Desc="GServer"/>
		<Class ClassID="50103" ClassName="sp_pc_skillpoint_increase" ParamCount="3" Desc="ZServer"/>
		<Class ClassID="50104" ClassName="sp_pc_mypage_comment_write" ParamCount="7" Desc="ZServer"/>
		<Class ClassID="50105" ClassName="sp_pc_mypage_comments" ParamCount="4" Desc="ZServer"/>
		<Class ClassID="50106" ClassName="sp_pc_help_add" ParamCount="3" Desc="ZServer"/>
		<Class ClassID="50107" ClassName="sp_pc_item_equip_create" ParamCount="5" Desc="ZServer"/>
		<Class ClassID="50108" ClassName="sp_pc_instant_event_finish" ParamCount="3" Desc="ZServer"/>
		<Class ClassID="50109" ClassName="sp_pc_help_read" ParamCount="3" Desc="ZServer"/>
		<Class ClassID="50110" ClassName="sp_pc_instant_event_remove" ParamCount="2" Desc="ZServer"/>
		<Class ClassID="50111" ClassName="sp_pc_guestbook_comment_write" ParamCount="7" Desc="ZServer"/>
		<Class ClassID="50112" ClassName="sp_pc_guestbook_comments" ParamCount="4" Desc="ZServer"/>
		<Class ClassID="50113" ClassName="sp_pc_guestbook_comment_delete" ParamCount="2" Desc="ZServer"/>
		<Class ClassID="50114" ClassName="sp_pc_mypage_comment_delete" ParamCount="2" Desc="ZServer"/>
		<Class ClassID="50115" ClassName="sp_pc_object_xac_set" ParamCount="3" Desc="ZServer"/>
		<Class ClassID="50116" ClassName="sp_pc_shop_item_reg" ParamCount="8" Desc="ZServer"/>
		<Class ClassID="50117" ClassName="sp_pc_shop_item_cancel" ParamCount="6" Desc="ZServer"/>
		<Class ClassID="50118" ClassName="uspCharacterShopItemEdit" ParamCount="8" Desc="ZServer"/>
		<Class ClassID="50119" ClassName="sp_pc_shop_item_buy" ParamCount="6" Desc="ZServer"/>
		<Class ClassID="50120" ClassName="sp_pc_shop_set" ParamCount="4" Desc="ZServer"/>
		<Class ClassID="50122" ClassName="sp_barrack_name_set" ParamCount="4" Desc="BServer"/>
		<Class ClassID="50123" ClassName="sp_barrack_pc_location_set" ParamCount="7" Desc="BServer"/>
		<Class ClassID="50124" ClassName="uspAccountGetAID" ParamCount="1" Desc="GServer"/>
		<Class ClassID="50125" ClassName="sp_ies_get_" ParamCount="1" Desc="GServer"/>
		<Class ClassID="50126" ClassName="sp_ies_account_set" ParamCount="5" Desc="GServer"/>
		<Class ClassID="50127" ClassName="sp_barrack_pc_pose_set" ParamCount="2" Desc="GServer"/>
		<Class ClassID="50128" ClassName="sp_barrack_object_set" ParamCount="11" Desc="GServer"/>
		<Class ClassID="50129" ClassName="sp_barrack_object_get" ParamCount="3" Desc="GServer"/>
		<Class ClassID="50130" ClassName="sp_barrack_object_remove" ParamCount="5" Desc="GServer"/>
		<Class ClassID="50132" ClassName="sp_treasure_monster_kill_set" ParamCount="4" Desc="GServer"/>
		<Class ClassID="50133" ClassName="sp_treasure_monster_kill_sub" ParamCount="4" Desc="GServer"/>
		<Class ClassID="50134" ClassName="sp_treasure_monster_kill_get" ParamCount="2" Desc="GServer"/>
		<Class ClassID="50135" ClassName="sp_treasure_monster_kill_get_all" ParamCount="0" Desc="GServer"/>
		<Class ClassID="50136" ClassName="sp_treasure_monster_time_set" ParamCount="4" Desc="GServer"/>
		<Class ClassID="50137" ClassName="sp_treasure_monster_time_get_all" ParamCount="0" Desc="GServer"/>
		<Class ClassID="50138" ClassName="sp_gm_auction_reg" ParamCount="8" Desc="GServer"/>
		<Class ClassID="50139" ClassName="sp_gm_auction_bid" ParamCount="4" Desc="GServer"/>
		<Class ClassID="50140" ClassName="sp_gm_auction_off" ParamCount="4" Desc="GServer"/>
		<Class ClassID="50141" ClassName="sp_gm_auction_group_get" ParamCount="1" Desc="GServer"/>
		<Class ClassID="50142" ClassName="sp_gm_auction_item_get" ParamCount="3" Desc="GServer"/>
		<Class ClassID="50143" ClassName="sp_gm_auction_lose_get" ParamCount="1" Desc="GServer"/>
		<Class ClassID="50144" ClassName="sp_gm_auction_money_get" ParamCount="5" Desc="GServer"/>
		<Class ClassID="50145" ClassName="sp_pc_relation_set" ParamCount="4" Desc="GServer"/>
		<Class ClassID="50146" ClassName="sp_pc_relation_remove" ParamCount="3" Desc="GServer"/>
		<Class ClassID="50147" ClassName="sp_pc_relation_list" ParamCount="1" Desc="GServer"/>
		<Class ClassID="50148" ClassName="sp_companion_set" ParamCount="8" Desc="ZServer"/>
		<Class ClassID="50149" ClassName="sp_pc_prop_group_set" ParamCount="4" Desc="ZServer"/>
		<Class ClassID="50150" ClassName="sp_ies_pcetc_set" ParamCount="5" Desc="ZServer"/>
		<Class ClassID="50151" ClassName="sp_pc_collection_add" ParamCount="3" Desc="ZServer"/>
		<Class ClassID="50152" ClassName="sp_pc_collection_item_put" ParamCount="6" Desc="ZServer"/>
		<Class ClassID="50153" ClassName="sp_pc_collection_item_take" ParamCount="6" Desc="ZServer"/>
		<Class ClassID="50157" ClassName="RServerIESAddPCEtc" ParamCount="4" Desc="ZServer"/>
		<Class ClassID="50158" ClassName="uspPeriodicTitleSet" ParamCount="5" Desc="ZServer"/>
		<Class ClassID="50159" ClassName="uspPeriodicTitleRemove" ParamCount="2" Desc="ZServer"/>
		<Class ClassID="50160" ClassName="sp_pc_job_playtime_set" ParamCount="6" Desc="ZServer"/>
		<Class ClassID="50161" ClassName="sp_market_item_reg_4" ParamCount="13" Desc="ZServer"/>
		<Class ClassID="50162" ClassName="sp_market_item_list_3" ParamCount="2" Desc="ZServer"/>
		<Class ClassID="50163" ClassName="sp_market_item_buy_6" ParamCount="7" Desc="ZServer"/>
		<Class ClassID="50164" ClassName="sp_market_item_cancel_2" ParamCount="3" Desc="ZServer"/>
		<Class ClassID="50165" ClassName="sp_pc_item_cabinet_get_4" ParamCount="1" Desc="ZServer"/>
		<Class ClassID="50166" ClassName="sp_pc_item_cabinet_take_2" ParamCount="6" Desc="ZServer"/>
		<Class ClassID="50167" ClassName="sp_sns_idx_save" ParamCount="4" Desc="ZServer"/>
		<Class ClassID="50168" ClassName="sp_pc_info_chat" ParamCount="2" Desc="ZServer"/>
		<Class ClassID="50169" ClassName="sp_barrack_name_check" ParamCount="2" Desc="ZServer"/>
		<Class ClassID="50170" ClassName="sp_friend_add" ParamCount="3" Desc="ZServer"/>
		<Class ClassID="50171" ClassName="sp_friend_accept" ParamCount="3" Desc="ZServer"/>
		<Class ClassID="50172" ClassName="sp_friend_remove" ParamCount="3" Desc="ZServer"/>
		<Class ClassID="50173" ClassName="sp_friend_deny" ParamCount="3" Desc="ZServer"/>
		<Class ClassID="50174" ClassName="sp_barrack_name_get" ParamCount="2" Desc="ZServer"/>
		<Class ClassID="50175" ClassName="sp_companion_follow_set" ParamCount="2" Desc="ZServer"/>
		<Class ClassID="50178" ClassName="sp_account_permission_get" ParamCount="1" Desc="ZServer"/>
		<Class ClassID="50179" ClassName="sp_companion_prop_set" ParamCount="5" Desc="ZServer"/>
		<Class ClassID="50180" ClassName="sp_companion_item_equip" ParamCount="5" Desc="ZServer"/>
		<Class ClassID="50181" ClassName="sp_companion_item_unequip" ParamCount="6" Desc="ZServer"/>
		<Class ClassID="50182" ClassName="sp_companion_exp_add_1" ParamCount="3" Desc="ZServer"/>
		<Class ClassID="50183" ClassName="sp_pc_battle_get" ParamCount="1" Desc="ZServer"/>
		<Class ClassID="50184" ClassName="sp_pc_battle_set" ParamCount="4" Desc="ZServer"/>
		<Class ClassID="50185" ClassName="sp_pc_battle_add" ParamCount="5" Desc="ZServer"/>
		<Class ClassID="50186" ClassName="sp_pc_battle_his_set" ParamCount="5" Desc="ZServer"/>
		<Class ClassID="50187" ClassName="sp_pc_battle_his_get" ParamCount="2" Desc="ZServer"/>
		<Class ClassID="50188" ClassName="sp_battle_rank_set" ParamCount="12" Desc="ZServer"/>
		<Class ClassID="50189" ClassName="sp_battle_rank_get" ParamCount="3" Desc="ZServer"/>
		<Class ClassID="50190" ClassName="sp_friend_block" ParamCount="3" Desc="ZServer"/>
		<Class ClassID="50191" ClassName="sp_friend_set" ParamCount="5" Desc="ZServer"/>
		<Class ClassID="50192" ClassName="sp_companion_delete" ParamCount="2" Desc="ZServer"/>
		<Class ClassID="50193" ClassName="sp_server_conf_get" ParamCount="0" Desc="ZServer"/>
		<Class ClassID="50194" ClassName="sp_barrack_connect" ParamCount="4" Desc="ZServer"/>
		<Class ClassID="50195" ClassName="sp_service_list" ParamCount="0" Desc="ZServer"/>
		<Class ClassID="50196" ClassName="sp_item_ies_prop_copy" ParamCount="3" Desc="ZServer"/>
		<Class ClassID="50197" ClassName="sp_pc_message_set" ParamCount="4" Desc="ZServer"/>
		<Class ClassID="50198" ClassName="sp_pc_camp_set" ParamCount="12" Desc="ZServer"/>
		<Class ClassID="50199" ClassName="sp_pc_camp_list" ParamCount="0" Desc="ZServer"/>
		<Class ClassID="50200" ClassName="sp_pc_camp_remove" ParamCount="3" Desc="ZServer"/>
		<Class ClassID="50201" ClassName="sp_pc_item_warehouse_slot_set" ParamCount="3" Desc="ZServer"/>
		<Class ClassID="50202" ClassName="sp_pc_item_warehouse_take_1" ParamCount="7" Desc="ZServer"/>
		<Class ClassID="50203" ClassName="sp_pc_item_warehouse_put_1" ParamCount="7" Desc="ZServer"/>
		<Class ClassID="50204" ClassName="sp_ies_map_channel_set" ParamCount="5" Desc="ZServer"/>
		<Class ClassID="50205" ClassName="sp_pc_buyshop_item_get" ParamCount="1" Desc="ZServer"/>
		<Class ClassID="50206" ClassName="sp_pc_camp_table_set_1" ParamCount="12" Desc="ZServer"/>
		<Class ClassID="50207" ClassName="sp_pc_camp_food_set" ParamCount="4" Desc="ZServer"/>
		<Class ClassID="50208" ClassName="sp_pc_camp_food_decrease" ParamCount="4" Desc="ZServer"/>
		<Class ClassID="50209" ClassName="sp_pc_camp_decamp_set" ParamCount="3" Desc="ZServer"/>
		<Class ClassID="50210" ClassName="sp_pc_camp_table_list_1" ParamCount="0" Desc="ZServer"/>
		<Class ClassID="50211" ClassName="sp_pc_camp_food_get" ParamCount="1" Desc="ZServer"/>
		<Class ClassID="50212" ClassName="sp_pc_buyshop_item_sell_1" ParamCount="7" Desc="ZServer"/>
		<Class ClassID="50213" ClassName="sp_pc_buyshop_silver_payall" ParamCount="4" Desc="ZServer"/>
		<Class ClassID="50214" ClassName="sp_pc_item_owner_get" ParamCount="1" Desc="ZServer"/>
		<Class ClassID="50215" ClassName="sp_account_gm_message_list" ParamCount="2" Desc="ZServer"/>
		<Class ClassID="50216" ClassName="sp_account_gm_message_get" ParamCount="3" Desc="ZServer"/>
		<Class ClassID="50217" ClassName="sp_account_gm_message_state_set" ParamCount="5" Desc="ZServer"/>
		<Class ClassID="50218" ClassName="sp_pc_item_bag" ParamCount="1" Desc="ZServer"/>
		<Class ClassID="50219" ClassName="sp_account_gm_message_item_take" ParamCount="6" Desc="ZServer"/>
		<Class ClassID="50220" ClassName="sp_pc_gm_cmd_list" ParamCount="2" Desc="ZServer"/>
		<Class ClassID="50221" ClassName="sp_gm_cmd_remove" ParamCount="1" Desc="ZServer"/>
		<Class ClassID="50222" ClassName="sp_account_like" ParamCount="4" Desc="ZServer"/>
		<Class ClassID="50223" ClassName="sp_account_like_remove" ParamCount="4" Desc="ZServer"/>
		<Class ClassID="50224" ClassName="sp_account_like_confirm" ParamCount="4" Desc="ZServer"/>
		<Class ClassID="50225" ClassName="sp_account_relation_get" ParamCount="2" Desc="ZServer"/>
		<Class ClassID="50226" ClassName="sp_account_relation_point_set" ParamCount="4" Desc="ZServer"/>
		<Class ClassID="50227" ClassName="sp_party_name_set" ParamCount="4" Desc="ZServer"/>
		<Class ClassID="50228" ClassName="sp_server_conf_set" ParamCount="2" Desc="ZServer"/>
		<Class ClassID="50229" ClassName="sp_pc_prop_group_ex_set" ParamCount="4" Desc="ZServer"/>
		<Class ClassID="50230" ClassName="sp_pc_prop_group_ex_get" ParamCount="1" Desc="ZServer"/>
		<Class ClassID="50231" ClassName="sp_item_lock" ParamCount="3" Desc="ZServer"/>
		<Class ClassID="50232" ClassName="sp_item_unlock" ParamCount="3" Desc="ZServer"/>
		<Class ClassID="50233" ClassName="sp_barrack_theme_set" ParamCount="5" Desc="GServer"/>
		<Class ClassID="50234" ClassName="sp_client_code_add" ParamCount="1" Desc="BServer"/>
		<Class ClassID="50235" ClassName="sp_client_code_get" ParamCount="0" Desc="BServer"/>
		<Class ClassID="50236" ClassName="sp_pc_gender_change" ParamCount="3" Desc="BServer"/>
		<Class ClassID="50237" ClassName="sp_pc_exp_set_1" ParamCount="3" Desc="ZServer"/>
		<Class ClassID="50244" ClassName="sp_pc_respec_set" ParamCount="3" Desc="ZServer"/>
		<Class ClassID="50245" ClassName="sp_pc_respec_get" ParamCount="1" Desc="ZServer"/>
		<Class ClassID="50246" ClassName="sp_market_item_cabinet_move_2" ParamCount="3" Desc="ZServer"/>
		<Class ClassID="50247" ClassName="sp_barrack_add_slot_get" ParamCount="2" Desc="BServer"/>
		<Class ClassID="50248" ClassName="sp_barrack_add_slot_set" ParamCount="4" Desc="BServer"/>
		<Class ClassID="50250" ClassName="sp_pc_skill_reset_1" ParamCount="2" Desc="ZServer"/>
		<Class ClassID="50251" ClassName="sp_pc_stat_reset_1" ParamCount="2" Desc="ZServer"/>
		<Class ClassID="50252" ClassName="sp_account_medal_his_add_4" ParamCount="12" Desc="ZServer"/>
		<Class ClassID="50253" ClassName="sp_account_medal_his_get_1" ParamCount="4" Desc="ZServer"/>
		<Class ClassID="50254" ClassName="sp_ies_pc_get" ParamCount="2" Desc="ZServer"/>
		<Class ClassID="50255" ClassName="sp_account_status_get_1" ParamCount="1" Desc="BServer, ZServer"/>
		<Class ClassID="50256" ClassName="sp_cash_token_get_2" ParamCount="2" Desc="ZServer"/>
		<Class ClassID="50257" ClassName="sp_cash_token_set_3" ParamCount="7" Desc="ZServer"/>
		<Class ClassID="50258" ClassName="sp_pc_camp_get" ParamCount="1" Desc="ZServer"/>
		<Class ClassID="50259" ClassName="sp_pc_cash_buff_amount_set" ParamCount="4" Desc="ZServer"/>
		<Class ClassID="50260" ClassName="sp_pc_cash_buff_amount_consume_time" ParamCount="4" Desc="ZServer"/>
		<Class ClassID="50261" ClassName="sp_pc_info_4" ParamCount="2" Desc="ZServer"/>
		<Class ClassID="50262" ClassName="sp_battle_rank_season_reset" ParamCount="0" Desc="ZServer"/>
		<Class ClassID="50263" ClassName="sp_battle_reset_rp_1" ParamCount="0" Desc="ZServer"/>
		<Class ClassID="50264" ClassName="sp_account_status_set_1" ParamCount="5" Desc="ZServer"/>
		<Class ClassID="50265" ClassName="sp_ies_account_add" ParamCount="5" Desc="GServer"/>
		<Class ClassID="50266" ClassName="sp_guild_item_list" ParamCount="1" Desc="GServer"/>
		<Class ClassID="50267" ClassName="sp_guild_warehouse_item_create_1" ParamCount="6" Desc="GServer"/>
		<Class ClassID="50268" ClassName="sp_guild_item_give_to_account" ParamCount="10" Desc="GServer"/>
		<Class ClassID="50269" ClassName="sp_account_postbox_get" ParamCount="1" Desc="GServer"/>
		<Class ClassID="50270" ClassName="sp_account_postbox_item_remove" ParamCount="2" Desc="GServer"/>
		<Class ClassID="50271" ClassName="sp_account_postbox_msg_remove" ParamCount="2" Desc="GServer"/>
		<Class ClassID="50272" ClassName="sp_account_item_warehouse_get" ParamCount="1" Desc="GServer"/>
		<Class ClassID="50273" ClassName="sp_account_item_warehouse_put" ParamCount="7" Desc="GServer"/>
		<Class ClassID="50274" ClassName="sp_account_item_warehouse_take" ParamCount="7" Desc="GServer"/>
		<Class ClassID="50275" ClassName="sp_account_item_warehouse_slot_set" ParamCount="3" Desc="GServer"/>
		<Class ClassID="50276" ClassName="sp_account_collection_add" ParamCount="3" Desc="GServer"/>
		<Class ClassID="50277" ClassName="sp_account_collection_item_put_1" ParamCount="7" Desc="GServer"/>
		<Class ClassID="50278" ClassName="sp_ies_item_get" ParamCount="1" Desc="GServer"/>
		<Class ClassID="50279" ClassName="sp_pc_item_cabinet_add_1" ParamCount="6" Desc="GServer"/>
		<Class ClassID="50280" ClassName="sp_guild_battle_get" ParamCount="1" Desc="ZServer"/>
		<Class ClassID="50281" ClassName="sp_guild_battle_set" ParamCount="4" Desc="ZServer"/>
		<Class ClassID="50282" ClassName="sp_guild_battle_add" ParamCount="5" Desc="ZServer"/>
		<Class ClassID="50283" ClassName="sp_pass_account_get" ParamCount="1" Desc="BServer"/>
		<Class ClassID="50284" ClassName="sp_stm_cash_dlc_purchase_reg" ParamCount="5" Desc="BServer"/>
		<Class ClassID="50285" ClassName="sp_stm_cash_dlc_purchase_set" ParamCount="4" Desc="BServer"/>
		<Class ClassID="50286" ClassName="sp_stm_cash_dlc_purchase_get" ParamCount="2" Desc="BServer"/>
		<Class ClassID="50287" ClassName="sp_pc_info_5" ParamCount="3" Desc="ZServer"/>
		<Class ClassID="50288" ClassName="sp_guild_war_neutrality_get" ParamCount="2" Desc="ZServer"/>
		<Class ClassID="50289" ClassName="sp_guild_war_neutrality_set_1" ParamCount="4" Desc="ZServer"/>
		<Class ClassID="50290" ClassName="sp_world_event_get_1" ParamCount="2" Desc="GServer"/>
		<Class ClassID="50291" ClassName="sp_pc_auto_macro_set" ParamCount="3" Desc="GServer"/>
		<Class ClassID="50292" ClassName="sp_pc_info_6" ParamCount="3" Desc="GServer"/>
		<Class ClassID="50293" ClassName="sp_account_postbox_msg_add" ParamCount="9" Desc="ZServer"/>
		<Class ClassID="50296" ClassName="sp_account_zone_info_2" ParamCount="3" Desc="GServer"/>
		<Class ClassID="50297" ClassName="sp_pc_mcc_info_8" ParamCount="3" Desc="ZServer"/>
		<Class ClassID="50298" ClassName="sp_account_shop_buy_limit_set_test" ParamCount="4" Desc="GServer"/>
		<Class ClassID="50299" ClassName="sp_account_shop_buy_limit_get_test" ParamCount="1" Desc="GServer"/>
		<Class ClassID="50300" ClassName="sp_pc_mcc_add" ParamCount="4" Desc="GServer"/>
		<Class ClassID="50301" ClassName="sp_pc_mcc_time_set" ParamCount="4" Desc="GServer"/>
		<Class ClassID="50302" ClassName="sp_pc_mcc_remove" ParamCount="2" Desc="GServer"/>
		<Class ClassID="50303" ClassName="sp_pc_mcc_get" ParamCount="1" Desc="GServer"/>
		<Class ClassID="50304" ClassName="sp_guild_item_give_to_account_2" ParamCount="11" Desc="ZServer"/>
		<Class ClassID="50305" ClassName="sp_guild_leader_change_1" ParamCount="5" Desc="ZServer"/>
		<Class ClassID="50306" ClassName="sp_party_all_list" ParamCount="1" Desc="ZServer"/>
		<Class ClassID="50307" ClassName="sp_battle_reset_count" ParamCount="0" Desc="ZServer"/>
		<Class ClassID="50308" ClassName="sp_battle_reset_rp_guild" ParamCount="2" Desc="ZServer"/>
		<Class ClassID="50309" ClassName="sp_battle_reset_rp_pc" ParamCount="2" Desc="ZServer"/>
		<Class ClassID="50310" ClassName="sp_market_report" ParamCount="3" Desc="ZServer"/>
		<Class ClassID="50311" ClassName="sp_battle_rank_get_all_season_top_ranker" ParamCount="2" Desc="ZServer"/>
		<Class ClassID="50312" ClassName="sp_account_barrack_create_time_get" ParamCount="3" Desc="BServer"/>
		<Class ClassID="50313" ClassName="sp_account_bot_set" ParamCount="3" Desc="ZServer"/>
		<Class ClassID="50314" ClassName="sp_account_bot_from_account_cnt_get" ParamCount="2" Desc="ZServer"/>
		<Class ClassID="50315" ClassName="sp_set_report_power" ParamCount="5" Desc="ZServer"/>
		<Class ClassID="50316" ClassName="sp_cash_ingame_add_1" ParamCount="7" Desc="ZServer"/>
		<Class ClassID="50317" ClassName="sp_cash_ingame_set" ParamCount="3" Desc="ZServer"/>
		<Class ClassID="50318" ClassName="sp_cash_ingame_get_1" ParamCount="2" Desc="ZServer"/>
		<Class ClassID="50319" ClassName="sp_guild_message_board_set" ParamCount="4" Desc="ZServer"/>
		<Class ClassID="50320" ClassName="sp_guild_message_board_get" ParamCount="1" Desc="ZServer"/>
		<Class ClassID="50322" ClassName="sp_cash_ingame_refund_add" ParamCount="6" Desc="ZServer"/>
		<Class ClassID="50323" ClassName="sp_cash_ingame_refund_set" ParamCount="3" Desc="ZServer"/>
		<Class ClassID="50324" ClassName="sp_market_item_cabinet_move_all" ParamCount="2" Desc="GServer"/>
		<Class ClassID="50325" ClassName="sp_pc_job_reset_2" ParamCount="3" Desc="ZServer"/>
		<Class ClassID="50326" ClassName="sp_account_item_warehouse_log_get" ParamCount="1" Desc="ZServer"/>
		<Class ClassID="50327" ClassName="sp_account_item_warehouse_log_set" ParamCount="5" Desc="ZServer"/>
		<Class ClassID="50328" ClassName="sp_item_class_list" ParamCount="0" Desc="BServer"/>
		<Class ClassID="50329" ClassName="sp_stt_indun_use_set" ParamCount="2" Desc="ZServer"/>
		<Class ClassID="50330" ClassName="sp_market_buy_item_get" ParamCount="1" Desc="ZServer"/>
		<Class ClassID="50335" ClassName="sp_account_fishing_item_bag_get" ParamCount="1" Desc="ZServer"/>
		<Class ClassID="50336" ClassName="sp_account_fishing_item_bag_put" ParamCount="6" Desc="ZServer"/>
		<Class ClassID="50337" ClassName="sp_account_fishing_item_bag_take" ParamCount="7" Desc="ZServer"/>
		<Class ClassID="50338" ClassName="sp_treasure_monster_kill_add_1" ParamCount="4" Desc="GServer"/>
		<Class ClassID="50342" ClassName="sp_chn_cn_master_get" ParamCount="3" Desc="BServer"/>
		<Class ClassID="50343" ClassName="sp_world_event_set_1" ParamCount="8" Desc="GServer"/>
		<Class ClassID="50344" ClassName="sp_account_fishing_rank_restore_set" ParamCount="4" Desc="ZServer"/>
		<Class ClassID="50345" ClassName="sp_world_event_ingame_set" ParamCount="5" Desc="GServer"/>
		<Class ClassID="50346" ClassName="sp_world_event_ingame_get" ParamCount="1" Desc="GServer"/>
		<Class ClassID="50348" ClassName="sp_account_wiki_achieve_add" ParamCount="3" Desc="ZServer"/>
		<Class ClassID="50349" ClassName="sp_account_wiki_item_shop_add" ParamCount="4" Desc="ZServer"/>
		<Class ClassID="50350" ClassName="sp_account_wiki_item_make_add" ParamCount="4" Desc="ZServer"/>
		<Class ClassID="50351" ClassName="sp_account_wiki_indun_add" ParamCount="4" Desc="ZServer"/>
		<Class ClassID="50352" ClassName="sp_account_wiki_monster_item_add" ParamCount="5" Desc="ZServer"/>
		<Class ClassID="50353" ClassName="sp_account_wiki_monster_kill_add" ParamCount="4" Desc="ZServer"/>
		<Class ClassID="50354" ClassName="sp_account_advbook_quest_set_test" ParamCount="3" Desc="ZServer"/>
		<Class ClassID="50355" ClassName="sp_account_wiki_get" ParamCount="2" Desc="ZServer"/>
		<Class ClassID="50356" ClassName="sp_account_wiki_item_add" ParamCount="6" Desc="ZServer"/>
		<Class ClassID="50357" ClassName="sp_account_wiki_item_option_add" ParamCount="5" Desc="ZServer"/>
		<Class ClassID="50358" ClassName="sp_account_wiki_fishing_add" ParamCount="4" Desc="ZServer"/>
		<Class ClassID="50359" ClassName="sp_account_wiki_reset" ParamCount="1" Desc="GServer"/>
		<Class ClassID="50360" ClassName="sp_account_wiki_point_set_1" ParamCount="4" Desc="ZServer"/>
		<Class ClassID="50361" ClassName="sp_account_wiki_point_get_2" ParamCount="1" Desc="GServer"/>
		<Class ClassID="50362" ClassName="sp_account_wiki_reward_set" ParamCount="4" Desc="ZServer"/>
		<Class ClassID="50363" ClassName="sp_account_wiki_reward_get" ParamCount="2" Desc="ZServer"/>
		<Class ClassID="50364" ClassName="sp_account_quest_get" ParamCount="3" Desc="ZServer"/>
		<Class ClassID="50365" ClassName="sp_account_fishing_rubbing_appearance_set" ParamCount="4" Desc="GServer"/>
		<Class ClassID="50366" ClassName="sp_account_fishing_rubbing_appearance_get" ParamCount="2" Desc="GServer"/>
		<Class ClassID="50367" ClassName="sp_guild_bank_log_set" ParamCount="6" Desc="ZServer/GServer"/>
		<Class ClassID="50368" ClassName="sp_guild_bank_log_get" ParamCount="2" Desc="ZServer"/>
		<Class ClassID="50369" ClassName="sp_account_wiki_point_contents_get" ParamCount="2" Desc="ZServer"/>
		<Class ClassID="50370" ClassName="sp_account_wiki_point_contents_achieve_set" ParamCount="6" Desc="ZServer"/>
		<Class ClassID="50371" ClassName="sp_account_wiki_point_contents_quest_set" ParamCount="4" Desc="ZServer"/>
		<Class ClassID="50372" ClassName="sp_guild_emblem_get" ParamCount="1" Desc="ZServer"/>
		<Class ClassID="50373" ClassName="sp_guild_emblem_set" ParamCount="3" Desc="ZServer"/>
		<Class ClassID="50374" ClassName="sp_guild_warehouse_item_remove" ParamCount="4" Desc="GServer"/>
		<Class ClassID="50375" ClassName="sp_account_medal_limit_set" ParamCount="5" Desc="ZServer"/>
		<Class ClassID="50376" ClassName="sp_account_medal_limit_get" ParamCount="2" Desc="ZServer"/>
		<Class ClassID="50377" ClassName="sp_companion_name_set" ParamCount="3" Desc="ZServer"/>
		<Class ClassID="50378" ClassName="sp_pc_job_remove_first_job_1" ParamCount="4" Desc="ZServer"/>
		<Class ClassID="50379" ClassName="sp_pc_job_remove_another_job_2" ParamCount="7" Desc="ZServer"/>
		<Class ClassID="50380" ClassName="sp_account_attendance_get" ParamCount="2" Desc="ZServer"/>
		<Class ClassID="50381" ClassName="sp_account_attendance_set" ParamCount="5" Desc="ZServer"/>
		<Class ClassID="50382" ClassName="sp_account_attendance_reward_get" ParamCount="3" Desc="ZServer"/>
		<Class ClassID="50383" ClassName="sp_account_attendance_reward_set" ParamCount="4" Desc="ZServer"/>
		<Class ClassID="50384" ClassName="sp_account_attendance_expire" ParamCount="1" Desc="GServer"/>
		<Class ClassID="50387" ClassName="sp_account_wiki_pc_search" ParamCount="2" Desc="ZServer"/>
		<Class ClassID="50388" ClassName="sp_barrack_pc_name_get" ParamCount="2" Desc="ZServer"/>
		<Class ClassID="50389" ClassName="sp_pc_name_get" ParamCount="2" Desc="ZServer"/>
		<Class ClassID="50390" ClassName="sp_guild_member_info_get" ParamCount="1" Desc="GServer"/>
		<Class ClassID="50391" ClassName="sp_guild_emblem_remove" ParamCount="2" Desc="ZServer"/>
		<Class ClassID="50392" ClassName="sp_account_map_class_list" ParamCount="0" Desc="BServer"/>
		<Class ClassID="50393" ClassName="sp_pc_item_warehouse_slot_swap" ParamCount="6" Desc="ZServer"/>
		<Class ClassID="50394" ClassName="sp_account_pcbang_point_get" ParamCount="1" Desc="ZServer"/>
		<Class ClassID="50395" ClassName="sp_account_pcbang_point_set" ParamCount="5" Desc="ZServer"/>
		<Class ClassID="50396" ClassName="sp_pointshop_buy_count_get" ParamCount="2" Desc="ZServer"/>
		<Class ClassID="50397" ClassName="sp_pointshop_buy_count_set" ParamCount="5" Desc="ZServer"/>
		<Class ClassID="50398" ClassName="sp_pointshop_buy_lobby_log_get" ParamCount="2" Desc="ZServer"/>
		<Class ClassID="50399" ClassName="sp_pointshop_buy_lobby_log_set" ParamCount="5" Desc="ZServer"/>
		<Class ClassID="50400" ClassName="sp_pointshop_buy_lobby_log_del" ParamCount="2" Desc="ZServer"/>
		<Class ClassID="50401" ClassName="sp_pointshop_buy_world_log_get" ParamCount="1" Desc="ZServer"/>
		<Class ClassID="50402" ClassName="sp_pointshop_buy_world_log_set" ParamCount="4" Desc="ZServer"/>
		<Class ClassID="50403" ClassName="sp_pointshop_buy_world_log_del" ParamCount="2" Desc="ZServer"/>
		<Class ClassID="50404" ClassName="sp_account_beautyshop_get" ParamCount="1" Desc="ZServer"/>
		<Class ClassID="50405" ClassName="sp_account_beautyshop_set" ParamCount="4" Desc="ZServer"/>
		<Class ClassID="50406" ClassName="sp_account_beauty_coupon_set" ParamCount="3" Desc="ZServer"/>
		<Class ClassID="50407" ClassName="sp_account_beauty_coupon_get" ParamCount="1" Desc="ZServer"/>
		<Class ClassID="50408" ClassName="sp_item_property_reserve_get" ParamCount="1" Desc="ZServer"/>
		<Class ClassID="50409" ClassName="sp_item_property_reserve_add" ParamCount="5" Desc="ZServer"/>
		<Class ClassID="50410" ClassName="sp_item_property_reserve_remove" ParamCount="2" Desc="ZServer"/>
		<Class ClassID="50411" ClassName="sp_solo_dungeon_get" ParamCount="2" Desc="ZServer"/>
		<Class ClassID="50412" ClassName="sp_solo_dungeon_set" ParamCount="5" Desc="ZServer"/>
		<Class ClassID="50413" ClassName="sp_pc_slot_swap" ParamCount="3" Desc="ZServer"/>
		<Class ClassID="50414" ClassName="sp_solo_dungeon_list_get" ParamCount="1" Desc="ZServer"/>
		<Class ClassID="50424" ClassName="sp_ies_item_reinforce_set" ParamCount="4" Desc="ZServer"/>
		<Class ClassID="50425" ClassName="sp_ies_item_transcend_set" ParamCount="4" Desc="ZServer"/>
		<Class ClassID="50427" ClassName="sp_indun_report_set_1" ParamCount="7" Desc="ZServer"/>
		<Class ClassID="50428" ClassName="sp_indun_report_penalty_get" ParamCount="1" Desc="ZServer"/>
		<Class ClassID="50429" ClassName="sp_indun_report_get" ParamCount="1" Desc="ZServer"/>
		<Class ClassID="50430" ClassName="sp_indun_report_penalty_set" ParamCount="4" Desc="ZServer"/>
		<Class ClassID="50431" ClassName="sp_indun_report_penalty_del" ParamCount="2" Desc="ZServer"/>
		<Class ClassID="50432" ClassName="sp_indun_report_reset" ParamCount="4" Desc="ZServer"/>
		<Class ClassID="50433" ClassName="sp_indun_reporter_get" ParamCount="1" Desc="ZServer"/>
		<Class ClassID="50434" ClassName="sp_indun_reporter_set" ParamCount="3" Desc="GServer"/>
		<Class ClassID="50435" ClassName="sp_colony_market_fee_set" ParamCount="3" Desc="ZServer"/>
		<Class ClassID="50436" ClassName="sp_colony_market_fee_get" ParamCount="1" Desc="ZServer, GServer"/>
		<Class ClassID="50437" ClassName="sp_colony_market_fee_payment_get" ParamCount="1" Desc="ZServer"/>
		<Class ClassID="50438" ClassName="sp_colony_market_fee_payment_list_get" ParamCount="1" Desc="ZServer"/>
		<Class ClassID="50439" ClassName="sp_colony_market_fee_payment_set" ParamCount="7" Desc="ZServer"/>
		<Class ClassID="50440" ClassName="sp_colony_market_fee_payment_flag_set" ParamCount="5" Desc="ZServer"/>
		<Class ClassID="50441" ClassName="sp_silver_pc_bag_set" ParamCount="4" Desc="ZServer"/>
		<Class ClassID="50442" ClassName="sp_silver_account_warehouse_set" ParamCount="4" Desc="ZServer"/>
		<Class ClassID="50443" ClassName="sp_silver_pc_bag_get" ParamCount="2" Desc="ZServer"/>
		<Class ClassID="50444" ClassName="sp_silver_account_warehouse_get" ParamCount="2" Desc="ZServer"/>
		<Class ClassID="50445" ClassName="sp_silver_pc_cabinet_get" ParamCount="2" Desc="ZServer"/>
		<Class ClassID="50446" ClassName="sp_silver_pc_cabinet_take" ParamCount="4" Desc="ZServer"/>
		<Class ClassID="50447" ClassName="sp_silver_guild_bag_get" ParamCount="2" Desc="ZServer"/>
		<Class ClassID="50448" ClassName="sp_silver_guild_bag_set" ParamCount="4" Desc="ZServer"/>
		<Class ClassID="50449" ClassName="sp_stm_friend_invite_get" ParamCount="2" Desc="ZServer"/>
		<Class ClassID="50500" ClassName="sp_market_trade_limit_set_1" ParamCount="4" Desc="ZServer"/>
		<Class ClassID="50501" ClassName="sp_market_trade_limit_get" ParamCount="2" Desc="ZServer"/>
		<Class ClassID="50502" ClassName="sp_account_trust_set" ParamCount="3" Desc="ZServer"/>
		<Class ClassID="50503" ClassName="sp_account_trust_get" ParamCount="2" Desc="ZServer"/>
		<Class ClassID="50504" ClassName="test_sp_market_item_reg" ParamCount="12" Desc="ZServer"/>
		<Class ClassID="50507" ClassName="sp_silver_pc_cabinet_set" ParamCount="3" Desc="ZServer"/>
	</Category>
	<Category Name="Logger">
		<Class ClassID="60000" ClassName="sp_server_logging_list" ParamCount="0" Desc="ZServer"/>
		<Class ClassID="60001" ClassName="CharacterSkillUse" ParamCount="12" Desc="ZServer"/>
		<Class ClassID="60002" ClassName="AccountLogin" ParamCount="3" Desc="ZServer"/>
		<Class ClassID="60003" ClassName="AccountLogout" ParamCount="7" Desc="ZServer"/>
		<Class ClassID="60004" ClassName="CharacterAchieve" ParamCount="8" Desc="ZServer"/>
		<Class ClassID="60005" ClassName="CharacterBarrackIn" ParamCount="7" Desc="ZServer"/>
		<Class ClassID="60006" ClassName="CharacterChangeJob" ParamCount="12" Desc="ZServer"/>
		<Class ClassID="60007" ClassName="CharacterChangeName" ParamCount="11" Desc="ZServer"/>
		<Class ClassID="60008" ClassName="CharacterCreate" ParamCount="10" Desc="ZServer"/>
		<Class ClassID="60009" ClassName="CharacterDeadByMon" ParamCount="15" Desc="ZServer"/>
		<Class ClassID="60010" ClassName="CharacterDeadByPk" ParamCount="20" Desc="ZServer"/>
		<Class ClassID="60011" ClassName="CharacterPkKill" ParamCount="17" Desc="ZServer"/>
		<Class ClassID="60012" ClassName="CharacterDelete" ParamCount="10" Desc="ZServer"/>
		<Class ClassID="60013" ClassName="CharacterExpAdd" ParamCount="10" Desc="ZServer"/>
		<Class ClassID="60014" ClassName="CharacterJobExpAdd" ParamCount="10" Desc="ZServer"/>
		<Class ClassID="60015" ClassName="CharacterLevelUp" ParamCount="10" Desc="ZServer"/>
		<Class ClassID="60016" ClassName="CharacterJobLevelUp" ParamCount="10" Desc="ZServer"/>
		<Class ClassID="60017" ClassName="CharacterMonsterKill" ParamCount="12" Desc="ZServer"/>
		<Class ClassID="60018" ClassName="CharacterOpenWindow" ParamCount="11" Desc="ZServer"/>
		<Class ClassID="60019" ClassName="CharacterQuestProgress" ParamCount="13" Desc="ZServer"/>
		<Class ClassID="60020" ClassName="CharacterQuickslotIn" ParamCount="8" Desc="ZServer"/>
		<Class ClassID="60021" ClassName="CharacterQuickslotOut" ParamCount="8" Desc="ZServer"/>
		<Class ClassID="60022" ClassName="CharacterResurrection" ParamCount="10" Desc="ZServer"/>
		<Class ClassID="60023" ClassName="CharacterSkillMapAddLog" ParamCount="10" Desc="ZServer"/>
		<Class ClassID="60024" ClassName="CharacterZoneIn" ParamCount="7" Desc="ZServer"/>
		<Class ClassID="60025" ClassName="CharacterZoneOut" ParamCount="7" Desc="ZServer"/>
		<Class ClassID="60026" ClassName="ItemDelete" ParamCount="15" Desc="ZServer"/>
		<Class ClassID="60027" ClassName="ItemEnchant" ParamCount="10" Desc="ZServer"/>
		<Class ClassID="60028" ClassName="ItemEquip" ParamCount="11" Desc="ZServer"/>
		<Class ClassID="60029" ClassName="ItemUnEquip" ParamCount="11" Desc="ZServer"/>
		<Class ClassID="60030" ClassName="NpcClick" ParamCount="7" Desc="ZServer"/>
		<Class ClassID="60031" ClassName="PartyCreate" ParamCount="8" Desc="ZServer"/>
		<Class ClassID="60032" ClassName="PartyDelete" ParamCount="6" Desc="ZServer"/>
		<Class ClassID="60034" ClassName="PartyJoin" ParamCount="8" Desc="ZServer"/>
		<Class ClassID="60035" ClassName="PartyLeave" ParamCount="9" Desc="ZServer"/>
		<Class ClassID="60036" ClassName="PartyStorageItemGet" ParamCount="15" Desc="ZServer"/>
		<Class ClassID="60037" ClassName="PartyStorageItemPut" ParamCount="15" Desc="ZServer"/>
		<Class ClassID="60038" ClassName="ItemGetFieldByLuckRatio" ParamCount="13" Desc="ZServer"/>
		<Class ClassID="60039" ClassName="sp_pc_hired_clear" ParamCount="1" Desc="ZServer"/>
		<Class ClassID="60040" ClassName="sp_pc_hired_add" ParamCount="2" Desc="ZServer"/>
		<Class ClassID="60041" ClassName="sp_server_option_list" ParamCount="0" Desc="ZServer"/>
		<Class ClassID="60042" ClassName="sp_pc_buff_del" ParamCount="3" Desc="ZServer"/>
		<Class ClassID="60043" ClassName="sp_guild_colony_boss_kill_list" ParamCount="1" Desc="ZServer"/>
	</Category>
</idspace>
