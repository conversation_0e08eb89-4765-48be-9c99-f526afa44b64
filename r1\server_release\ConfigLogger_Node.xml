<!-- edited with XMLSPY v2004 rel. 3 U (http://www.xmlspy.com) by imc (imc) -->
<Loggers Update="YES">
	<!-- root 로거는 반드시 존재해야 한다. 없다면 기본 root로거 설정을 사용한다 -->
	<Logger Name="root" Parent ="" Level="DEBUG">
		<Appender Type="Console" Name="Console" Level="DEBUG">
			<Layout Type="Pattern" Pattern="[%d{yyyy-MM-dd HH:mm:ss.SSS}] [%p] %F(%L) : [%X{ErrorCode}] %m %X %n%X{CallStack}"/>
		</Appender>
		<Appender Type="DailyRollingFile" Name="RollingFile" Level="ALL" FileName="tos" FilePath="./log_node" Pattern="'.'yyyy.MM.dd">
			<Layout Type="Pattern" Pattern="[%d{yyyy-MM-dd HH:mm:ss.SSS}] [%p] %F(%L) : [%X{ErrorCode}] %m %X %n%X{CallStack}"/>
		</Appender>
		<!-- TCP日志已禁用 - 避免连接错误
		<Appender Type="TCP" Name="GrayLog" Level="ALL">
			<Layout Type="GELF" LocationInfo="YES" Properties="YES" ServerInfo="YES"/>
		</Appender>
		-->
	</Logger>
	<Logger Name="mongoFailOver" Parent ="" Level="ERROR">
		<Appender Type="DailyRollingFile" Name="MongoFailOverFile" Level="ALL" FileName="MongoLog" FilePath="./mongo_failover" Pattern="'.'yyyy.MM.dd">
			<Layout Type="Pattern" Pattern="Mongo : %m%n"/>
		</Appender>
	</Logger>
</Loggers>