﻿<?xml version="1.0" encoding="utf-8"?>
<configuration>
  <configSections>
    <section name="log4net" type="log4net.Config.Log4NetConfigurationSectionHandler, log4net"/>
  </configSections>
  
    <startup> 
        <supportedRuntime version="v4.0" sku=".NETFramework,Version=v4.5.2" />
    </startup>
    <appSettings>
      <add key="GrayLogFacility" value="WebServer"/>
      <add key="GrayLogHost" value="***************:12202"/>
    </appSettings>
  
  <log4net>
    <root>
      <level value="DEBUG"/>
      <appender-ref ref="RollingLogFileAppender"/>
    </root>
    <appender name="RollingLogFileAppender" type="log4net.Appender.RollingFileAppender">
      <file type="log4net.Util.PatternString" value=".\\logs\\%date{yyyyMM}\\error.log" />
      <appendToFile value="true" />
      <rollingStyle value="Date" />      
      <maximumFileSize value="5MB" />
      <staticLogFileName value="true" />
      <preserveLogFileNameExtension value="true"/>
      <datePattern value="ddMMyyyy" />
      <layout type="log4net.Layout.PatternLayout">
        <conversionPattern value="%date [%thread] %-5level %logger - %message%newline" />
      </layout>
    </appender>
  </log4net>
  
  <connectionStrings>    
    <add name="world_idx" connectionString="9001" />
    <add name="Mariadb" connectionString="host=127.0.0.1;port=3306;database=tos_world1;" />
    <add name="Mariadb_lobby" connectionString="host=127.0.0.1;port=3306;database=tos_lobby;" />
    <add name="ServiceNation" connectionString="global" />
    <add name="serverManagerIP" connectionString="**************" />
    <add name="serverManagerPort" connectionString="8101"/>
    <add name="validIPList" connectionString="**************;127.0.0.1;::1;" />
    <add name="Use_Mongo" connectionString="no" />
    <add name="Use_Mongo_password" connectionString="no" />

    <add name="DataPath" connectionString="./../../../../data" />
    <add name="TpItemDirPath" connectionString="../resource/xml/tpitem" />
    <add name="OverrideDirPath" connectionString="../resource/override" />
    <add name="GuildExpXmlPath" connectionString="../resource/xml/guild/guildexp.xml" />
    <add name="IntroductionImagePath" connectionString="../Storage/image/introduction" />
  </connectionStrings>
  <runtime>
    <assemblyBinding xmlns="urn:schemas-microsoft-com:asm.v1">
      <dependentAssembly>
        <assemblyIdentity name="System.Runtime.InteropServices.RuntimeInformation" publicKeyToken="b03f5f7f11d50a3a" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-*******" newVersion="*******" />
      </dependentAssembly>
    </assemblyBinding>
  </runtime>
</configuration>
