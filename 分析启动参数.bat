@echo off
echo ================================
echo 分析启动参数和配置关系
echo ================================

echo [1] 分析 Hub 启动参数...
echo BAT文件内容:
type "r1\_1_HUB MID 1.bat"
echo.
echo 参数分析:
echo -PORT 9676    : Hub监听端口
echo -IP ************** : Hub绑定IP
echo -SGID 9001    : 服务器组ID
echo -MID 1        : 机器ID
echo -ROOT C:\r1   : 根目录
echo.

echo [2] 分析 Node 启动参数...
echo BAT文件内容:
type "r1\_2_NODE MID 1.bat"
echo.
echo 参数分析:
echo -IP ************** : 要连接的Hub IP
echo -PORT 9676    : Hub端口
echo -SGID 9001    : 服务器组ID
echo -OPEN_PORT 8102 : Node监听端口
echo -MID 1        : 机器ID
echo -ROOT C:\r1   : 根目录
echo.

echo [3] 问题分析...
echo 问题1: Node尝试连接外网IP的Hub (**************:9676)
echo 问题2: Node无法绑定端口8102 (可能被占用)
echo.
echo 解决方案:
echo 1. 如果是本地测试，Hub和Node的IP都应该是 127.0.0.1
echo 2. 如果是外网服务，Hub应该绑定 0.0.0.0，Node连接外网IP
echo 3. 检查端口占用情况
echo.

echo [4] 建议的配置...
echo 本地测试配置:
echo Hub: -IP 127.0.0.1 -PORT 9676
echo Node: -IP 127.0.0.1 -PORT 9676 -OPEN_PORT 8102
echo.
echo 外网服务配置:
echo Hub: -IP 0.0.0.0 -PORT 9676 (绑定所有接口)
echo Node: -IP ************** -PORT 9676 -OPEN_PORT 8102
echo.

pause